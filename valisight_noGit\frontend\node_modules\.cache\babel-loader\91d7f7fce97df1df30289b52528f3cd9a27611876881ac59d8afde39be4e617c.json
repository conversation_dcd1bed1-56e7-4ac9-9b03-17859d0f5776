{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JASON_NEW\\\\valisight_noGit\\\\frontend\\\\src\\\\pages\\\\reports\\\\ReportPages\\\\BalanceSheet.jsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BalanceSheetDashboard = ({\n  headerTextStyle = {},\n  subHeadingTextStyle = {},\n  contentTextStyle = {},\n  reportData = null // Add reportData prop to receive API data\n}) => {\n  // Extract background color from headerTextStyle for table header\n  const headerBgColor = headerTextStyle.color || '#20b2aa';\n\n  // Function to transform API data into the required format\n  const transformApiData = apiData => {\n    if (!apiData || !apiData.balanceSheetTableData) {\n      return {\n        tableData: [],\n        dateInfo: {\n          current: 'Jan 25',\n          previousYear: 'Jan 24',\n          previousMonth: 'Dec 24'\n        }\n      };\n    }\n    const rawData = apiData.balanceSheetTableData;\n\n    // Extract date information from the first item's keys\n    let currentDate = 'Jan 25';\n    let previousYear = 'Jan 24';\n    let previousMonth = 'Dec 24';\n    if (rawData.length > 0) {\n      const keys = Object.keys(rawData[0]);\n\n      // Extract current date\n      const actualsKey = keys.find(key => key.includes('_Actuals'));\n      if (actualsKey) {\n        const dateMatch = actualsKey.match(/([A-Za-z]+_\\d+)_Actuals/);\n        if (dateMatch) {\n          currentDate = dateMatch[1].replace('_', ' ');\n\n          // Calculate previous year (same month, year-1)\n          const [month, year] = currentDate.split(' ');\n          previousYear = `${month} ${parseInt(year) - 1}`;\n        }\n      }\n\n      // Extract previous month\n      const priorMonthKey = keys.find(key => key.includes('_Prior_Month'));\n      if (priorMonthKey) {\n        const monthMatch = priorMonthKey.match(/([A-Za-z]+_\\d+)_Prior_Month/);\n        if (monthMatch) {\n          previousMonth = monthMatch[1].replace('_', ' ');\n        }\n      }\n    }\n\n    // Group data by accountClassification and then by account_type\n    const classificationGroups = rawData.reduce((acc, item) => {\n      const classification = item.accountClassification;\n      const accountType = item.account_type;\n      if (!acc[classification]) {\n        acc[classification] = {};\n      }\n      if (!acc[classification][accountType]) {\n        acc[classification][accountType] = [];\n      }\n      acc[classification][accountType].push(item);\n      return acc;\n    }, {});\n\n    // Transform grouped data into table format\n    const tableData = [];\n    Object.keys(classificationGroups).forEach(classification => {\n      // Add main category header (Asset, Liability, Equity)\n      tableData.push({\n        isMainCategory: true,\n        category: classification\n      });\n      const accountTypes = classificationGroups[classification];\n      Object.keys(accountTypes).forEach(accountType => {\n        // Add sub-category header (account type)\n        tableData.push({\n          isSubCategory: true,\n          category: accountType\n        });\n\n        // Add individual account rows\n        accountTypes[accountType].forEach(item => {\n          // Check if this is a total row (contains \"Total\" in account_name)\n          const isTotal = item.account_name.toLowerCase().includes('total');\n\n          // Determine if variance is negative for styling\n          const priorYearVariance = parseFloat(item.Variance_Prior_Year || '0');\n          const priorMonthVariance = parseFloat(item.Variance_Prior_Month || '0');\n          const isNegativePriorYear = priorYearVariance < 0;\n          const isNegativePriorMonth = priorMonthVariance < 0;\n          tableData.push({\n            label: item.account_name,\n            jan25: item.Apr_25_Actuals || '0',\n            // Current period actuals\n            jan25Percent: item.Apr_25_Actuals || '0',\n            // Using actuals for percentage column\n            jan24: item.Apr_24_Prior_Year || '0',\n            // Prior year\n            jan24Percent: item.Variance_Prior_Year || '0',\n            // Prior year variance\n            variance: item.Mar_25_Prior_Month || '0',\n            // Prior month\n            variancePercent: item.Variance_Prior_Month || '0',\n            // Prior month variance\n            isTotal: isTotal,\n            isGrandTotal: item.account_name.toLowerCase().includes('total') && (classification.toLowerCase().includes('asset') || classification.toLowerCase().includes('total')),\n            isNegative: isNegativePriorMonth // Use prior month variance for styling\n          });\n        });\n\n        // Add total row for each account type if needed\n        const totalRow = accountTypes[accountType].find(item => item.account_name.toLowerCase().includes('total'));\n        if (!totalRow && accountTypes[accountType].length > 1) {\n          // Calculate totals if not provided in API\n          const totals = accountTypes[accountType].reduce((sum, item) => {\n            if (!item.account_name.toLowerCase().includes('total')) {\n              sum.actuals += parseFloat(item.Apr_25_Actuals || '0');\n              sum.priorYear += parseFloat(item.Apr_24_Prior_Year || '0');\n              sum.priorMonth += parseFloat(item.Mar_25_Prior_Month || '0');\n            }\n            return sum;\n          }, {\n            actuals: 0,\n            priorYear: 0,\n            priorMonth: 0\n          });\n          tableData.push({\n            label: `Total ${accountType}`,\n            jan25: totals.actuals.toString(),\n            jan25Percent: totals.actuals.toString(),\n            jan24: totals.priorYear.toString(),\n            jan24Percent: (totals.actuals - totals.priorYear).toString(),\n            variance: totals.priorMonth.toString(),\n            variancePercent: (totals.actuals - totals.priorMonth).toString(),\n            isTotal: true,\n            isNegative: totals.actuals - totals.priorMonth < 0\n          });\n        }\n      });\n    });\n    return {\n      tableData,\n      dateInfo: {\n        current: currentDate,\n        previousYear: previousYear,\n        previousMonth: previousMonth\n      }\n    };\n  };\n\n  // Transform the API data\n  const {\n    tableData,\n    dateInfo\n  } = transformApiData(reportData);\n\n  // Fallback to empty state if no data\n  if (!reportData || !reportData.balanceSheetTableData || tableData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen p-5\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl bg-white p-10 mx-auto overflow-x-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: 'lighter',\n              color: \"black\"\n            },\n            children: \"Balance Sheet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: 'lighter',\n              color: \"black\"\n            },\n            children: \"Balance Sheet Report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              ...contentTextStyle,\n              fontSize: \"20px\"\n            },\n            children: \"Acme Print\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center h-64\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg text-gray-600\",\n              children: \"No data available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-500 mt-2\",\n              children: \"Please check your data source\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this);\n  }\n  const renderTableRow = (item, index) => {\n    if (item.isMainCategory) {\n      return /*#__PURE__*/_jsxDEV(\"tr\", {\n        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-left pl-2 font-bold text-gray-800\",\n          style: {\n            ...contentTextStyle,\n            fontSize: '15px'\n          },\n          children: item.category\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right\",\n          children: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right\",\n          children: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right\",\n          children: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right\",\n          children: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right\",\n          children: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this);\n    }\n    if (item.isSubCategory) {\n      return /*#__PURE__*/_jsxDEV(\"tr\", {\n        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-left pl-4 font-semibold text-gray-700\",\n          style: {\n            ...contentTextStyle,\n            fontSize: '15px'\n          },\n          children: item.category\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right\",\n          children: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right\",\n          children: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right\",\n          children: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right\",\n          children: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right\",\n          children: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this);\n    }\n    if (item.isAccountGroup) {\n      return /*#__PURE__*/_jsxDEV(\"tr\", {\n        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-left pl-6 font-medium text-gray-600\",\n          style: {\n            ...contentTextStyle,\n            fontSize: '15px'\n          },\n          children: item.category\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right\",\n          children: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right\",\n          children: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right\",\n          children: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right\",\n          children: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right\",\n          children: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this);\n    }\n    if (item.isGrandTotal) {\n      return /*#__PURE__*/_jsxDEV(\"tr\", {\n        className: \"border-t-2 border-gray \",\n        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-left pl-2 font-bold text-gray-900\",\n          style: {\n            ...contentTextStyle,\n            fontSize: '15px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right font-mono font-bold\",\n          style: {\n            ...contentTextStyle,\n            fontSize: '15px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: item.jan25\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right font-mono font-bold\",\n          style: {\n            ...contentTextStyle,\n            fontSize: '15px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: item.jan24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right font-mono font-bold\",\n          style: {\n            ...contentTextStyle,\n            fontSize: '15px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: item.jan24Percent\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right font-mono font-bold\",\n          style: {\n            ...contentTextStyle,\n            fontSize: '15px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: item.variance\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right font-bold\",\n          style: {\n            ...contentTextStyle,\n            fontSize: '15px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: item.variancePercent\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this);\n    }\n    if (item.isTotal) {\n      return /*#__PURE__*/_jsxDEV(\"tr\", {\n        className: \"border-t-2 border-gray\",\n        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-left pl-6 font-semibold\",\n          style: {\n            ...contentTextStyle,\n            fontSize: '15px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right font-mono\",\n          style: {\n            ...contentTextStyle,\n            fontSize: '15px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: item.jan25\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right font-mono\",\n          style: {\n            ...contentTextStyle,\n            fontSize: '15px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: item.jan24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right font-mono\",\n          style: {\n            ...contentTextStyle,\n            fontSize: '15px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: item.jan24Percent\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right font-mono\",\n          style: {\n            ...contentTextStyle,\n            fontSize: '15px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: item.variance\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: `text-right ${item.isNegative ? 'text-red-600' : ''}`,\n          style: {\n            ...contentTextStyle,\n            fontSize: '15px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: item.variancePercent\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"tr\", {\n      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"text-left pl-8 font-normal\",\n        style: {\n          ...contentTextStyle,\n          fontSize: '15px'\n        },\n        children: item.label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"text-right font-mono\",\n        style: {\n          ...contentTextStyle,\n          fontSize: '15px'\n        },\n        children: item.jan25Percent\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"text-right font-mono\",\n        style: {\n          ...contentTextStyle,\n          fontSize: '15px'\n        },\n        children: item.jan24\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"text-right font-mono\",\n        style: {\n          ...contentTextStyle,\n          fontSize: '15px'\n        },\n        children: item.jan24Percent\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"text-right font-mono\",\n        style: {\n          ...contentTextStyle,\n          fontSize: '15px'\n        },\n        children: item.variance\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: `text-right ${item.isNegative ? 'text-red-600' : ''}`,\n        style: {\n          ...contentTextStyle,\n          fontSize: '15px'\n        },\n        children: item.variancePercent\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this)]\n    }, index, true, {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Generate dynamic date description\n  const getDateDescription = () => {\n    if (dateInfo.current) {\n      const [month, year] = dateInfo.current.split(' ');\n      const monthNames = {\n        'Jan': 'January',\n        'Feb': 'February',\n        'Mar': 'March',\n        'Apr': 'April',\n        'May': 'May',\n        'Jun': 'June',\n        'Jul': 'July',\n        'Aug': 'August',\n        'Sep': 'September',\n        'Oct': 'October',\n        'Nov': 'November',\n        'Dec': 'December'\n      };\n      const fullMonth = monthNames[month] || month;\n      return `As of ${fullMonth} 31st, 20${year}`;\n    }\n    return 'As of January 31st, 2025';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen p-5\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl bg-white p-10 mx-auto overflow-x-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          style: {\n            ...subHeadingTextStyle,\n            fontWeight: 'lighter',\n            color: \"black\"\n          },\n          children: \"Balance Sheet\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...subHeadingTextStyle,\n            fontWeight: 'lighter',\n            color: \"black\"\n          },\n          children: getDateDescription()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...contentTextStyle,\n            fontSize: \"20px\"\n          },\n          children: \"Acme Print\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"w-full border-collapse text-sm mt-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"text-black text-center bg-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-left bg-white border-0\",\n                style: {\n                  ...contentTextStyle,\n                  fontSize: '15px',\n                  color: 'black'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-left bg-white border-0 pl-10\",\n                style: {\n                  ...contentTextStyle,\n                  fontSize: '15px',\n                  color: 'black'\n                },\n                colSpan: \"1\",\n                children: dateInfo.current\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-left bg-white border-0 pl-10\",\n                style: {\n                  ...contentTextStyle,\n                  fontSize: '15px',\n                  color: 'black'\n                },\n                colSpan: \"2\",\n                children: dateInfo.previousYear\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-left bg-white border-0 pl-10\",\n                style: {\n                  ...contentTextStyle,\n                  fontSize: '15px',\n                  color: 'black'\n                },\n                colSpan: \"2\",\n                children: dateInfo.previousMonth\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-right text-white p-2 font-bold text-sm\",\n                style: {\n                  ...contentTextStyle,\n                  fontSize: '15px',\n                  color: 'white'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-center text-white p-2 font-bold text-sm\",\n                style: {\n                  backgroundColor: headerBgColor,\n                  ...contentTextStyle,\n                  fontSize: '15px',\n                  color: 'white',\n                  borderRight: '20px solid white'\n                },\n                children: \"Actuals\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-center text-white p-1 font-bold text-sm\",\n                style: {\n                  backgroundColor: headerBgColor,\n                  ...contentTextStyle,\n                  fontSize: '15px',\n                  color: 'white'\n                },\n                children: \"Prior Year\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 413,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-center text-white p-1 font-bold text-sm\",\n                style: {\n                  backgroundColor: headerBgColor,\n                  ...contentTextStyle,\n                  fontSize: '15px',\n                  color: 'white',\n                  borderRight: '20px solid white'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"Variance\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-center text-white p-2 font-bold text-sm\",\n                style: {\n                  backgroundColor: headerBgColor,\n                  ...contentTextStyle,\n                  fontSize: '15px',\n                  color: 'white'\n                },\n                children: \"Prior Month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-center text-white p-2 font-bold text-sm\",\n                style: {\n                  backgroundColor: headerBgColor,\n                  ...contentTextStyle,\n                  fontSize: '15px',\n                  color: 'white'\n                },\n                children: \"Variance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: tableData.map((item, index) => renderTableRow(item, index))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center text-slate-300 text-xs border-b-4 border-blue-900 py-9\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"The information contained in this report is provided for informational purposes only and is not intended to substitute for obtaining accounting, tax, or financial advice from a professional accountant. Any tax advice contained in this report is not intended to be used for the purpose of avoiding penalties under tax law. While we use reasonable efforts to furnish accurate and up-to-date information, we do not warrant that any information contained in or made available through this report is accurate, complete, reliable, current or error-free. We assume no liability or responsibility for any errors or omissions in the content of this report or delivered information.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 335,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 333,\n    columnNumber: 5\n  }, this);\n};\n_c = BalanceSheetDashboard;\nexport default BalanceSheetDashboard;\nvar _c;\n$RefreshReg$(_c, \"BalanceSheetDashboard\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "BalanceSheetDashboard", "headerTextStyle", "subHeadingTextStyle", "contentTextStyle", "reportData", "headerBgColor", "color", "transformApiData", "apiData", "balanceSheetTableData", "tableData", "dateInfo", "current", "previousYear", "previousMonth", "rawData", "currentDate", "length", "keys", "Object", "actualsKey", "find", "key", "includes", "dateMatch", "match", "replace", "month", "year", "split", "parseInt", "prior<PERSON><PERSON><PERSON><PERSON><PERSON>", "monthMatch", "classificationGroups", "reduce", "acc", "item", "classification", "accountClassification", "accountType", "account_type", "push", "for<PERSON>ach", "isMainCategory", "category", "accountTypes", "isSubCategory", "isTotal", "account_name", "toLowerCase", "priorY<PERSON><PERSON><PERSON>ce", "parseFloat", "Variance_Prior_Year", "prior<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Variance_Prior_Month", "isNegativePriorYear", "isNegativePriorMonth", "label", "jan25", "Apr_25_Actuals", "jan25<PERSON><PERSON><PERSON>", "jan24", "Apr_24_Prior_Year", "jan24<PERSON>ercent", "variance", "Mar_25_Prior_Month", "variancePercent", "isGrandTotal", "isNegative", "totalRow", "totals", "sum", "actuals", "priorYear", "<PERSON><PERSON><PERSON><PERSON>", "toString", "className", "children", "style", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "renderTableRow", "index", "isAccountGroup", "getDateDescription", "monthNames", "fullMonth", "colSpan", "backgroundColor", "borderRight", "map", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/JASON_NEW/valisight_noGit/frontend/src/pages/reports/ReportPages/BalanceSheet.jsx"], "sourcesContent": ["import React from 'react';\r\n\r\nconst BalanceSheetDashboard = ({ \r\n  headerTextStyle = {},\r\n  subHeadingTextStyle = {},\r\n  contentTextStyle = {},\r\n  reportData = null // Add reportData prop to receive API data\r\n}) => {\r\n  // Extract background color from headerTextStyle for table header\r\n  const headerBgColor = headerTextStyle.color || '#20b2aa';\r\n\r\n  // Function to transform API data into the required format\r\n  const transformApiData = (apiData) => {\r\n    if (!apiData || !apiData.balanceSheetTableData) {\r\n      return { \r\n        tableData: [], \r\n        dateInfo: { \r\n          current: 'Jan 25', \r\n          previousYear: 'Jan 24', \r\n          previousMonth: 'Dec 24' \r\n        } \r\n      };\r\n    }\r\n\r\n    const rawData = apiData.balanceSheetTableData;\r\n    \r\n    // Extract date information from the first item's keys\r\n    let currentDate = 'Jan 25';\r\n    let previousYear = 'Jan 24';\r\n    let previousMonth = 'Dec 24';\r\n    \r\n    if (rawData.length > 0) {\r\n      const keys = Object.keys(rawData[0]);\r\n      \r\n      // Extract current date\r\n      const actualsKey = keys.find(key => key.includes('_Actuals'));\r\n      if (actualsKey) {\r\n        const dateMatch = actualsKey.match(/([A-Za-z]+_\\d+)_Actuals/);\r\n        if (dateMatch) {\r\n          currentDate = dateMatch[1].replace('_', ' ');\r\n          \r\n          // Calculate previous year (same month, year-1)\r\n          const [month, year] = currentDate.split(' ');\r\n          previousYear = `${month} ${parseInt(year) - 1}`;\r\n        }\r\n      }\r\n      \r\n      // Extract previous month\r\n      const priorMonthKey = keys.find(key => key.includes('_Prior_Month'));\r\n      if (priorMonthKey) {\r\n        const monthMatch = priorMonthKey.match(/([A-Za-z]+_\\d+)_Prior_Month/);\r\n        if (monthMatch) {\r\n          previousMonth = monthMatch[1].replace('_', ' ');\r\n        }\r\n      }\r\n    }\r\n\r\n    // Group data by accountClassification and then by account_type\r\n    const classificationGroups = rawData.reduce((acc, item) => {\r\n      const classification = item.accountClassification;\r\n      const accountType = item.account_type;\r\n      \r\n      if (!acc[classification]) {\r\n        acc[classification] = {};\r\n      }\r\n      if (!acc[classification][accountType]) {\r\n        acc[classification][accountType] = [];\r\n      }\r\n      acc[classification][accountType].push(item);\r\n      return acc;\r\n    }, {});\r\n\r\n    // Transform grouped data into table format\r\n    const tableData = [];\r\n    \r\n    Object.keys(classificationGroups).forEach(classification => {\r\n      // Add main category header (Asset, Liability, Equity)\r\n      tableData.push({\r\n        isMainCategory: true,\r\n        category: classification\r\n      });\r\n\r\n      const accountTypes = classificationGroups[classification];\r\n      \r\n      Object.keys(accountTypes).forEach(accountType => {\r\n        // Add sub-category header (account type)\r\n        tableData.push({\r\n          isSubCategory: true,\r\n          category: accountType\r\n        });\r\n\r\n        // Add individual account rows\r\n        accountTypes[accountType].forEach(item => {\r\n          // Check if this is a total row (contains \"Total\" in account_name)\r\n          const isTotal = item.account_name.toLowerCase().includes('total');\r\n          \r\n          // Determine if variance is negative for styling\r\n          const priorYearVariance = parseFloat(item.Variance_Prior_Year || '0');\r\n          const priorMonthVariance = parseFloat(item.Variance_Prior_Month || '0');\r\n          const isNegativePriorYear = priorYearVariance < 0;\r\n          const isNegativePriorMonth = priorMonthVariance < 0;\r\n          \r\n          tableData.push({\r\n            label: item.account_name,\r\n            jan25: item.Apr_25_Actuals || '0', // Current period actuals\r\n            jan25Percent: item.Apr_25_Actuals || '0', // Using actuals for percentage column\r\n            jan24: item.Apr_24_Prior_Year || '0', // Prior year\r\n            jan24Percent: item.Variance_Prior_Year || '0', // Prior year variance\r\n            variance: item.Mar_25_Prior_Month || '0', // Prior month\r\n            variancePercent: item.Variance_Prior_Month || '0', // Prior month variance\r\n            isTotal: isTotal,\r\n            isGrandTotal: item.account_name.toLowerCase().includes('total') && \r\n                         (classification.toLowerCase().includes('asset') || \r\n                          classification.toLowerCase().includes('total')),\r\n            isNegative: isNegativePriorMonth // Use prior month variance for styling\r\n          });\r\n        });\r\n\r\n        // Add total row for each account type if needed\r\n        const totalRow = accountTypes[accountType].find(item => \r\n          item.account_name.toLowerCase().includes('total')\r\n        );\r\n        \r\n        if (!totalRow && accountTypes[accountType].length > 1) {\r\n          // Calculate totals if not provided in API\r\n          const totals = accountTypes[accountType].reduce((sum, item) => {\r\n            if (!item.account_name.toLowerCase().includes('total')) {\r\n              sum.actuals += parseFloat(item.Apr_25_Actuals || '0');\r\n              sum.priorYear += parseFloat(item.Apr_24_Prior_Year || '0');\r\n              sum.priorMonth += parseFloat(item.Mar_25_Prior_Month || '0');\r\n            }\r\n            return sum;\r\n          }, { actuals: 0, priorYear: 0, priorMonth: 0 });\r\n          \r\n          tableData.push({\r\n            label: `Total ${accountType}`,\r\n            jan25: totals.actuals.toString(),\r\n            jan25Percent: totals.actuals.toString(),\r\n            jan24: totals.priorYear.toString(),\r\n            jan24Percent: (totals.actuals - totals.priorYear).toString(),\r\n            variance: totals.priorMonth.toString(),\r\n            variancePercent: (totals.actuals - totals.priorMonth).toString(),\r\n            isTotal: true,\r\n            isNegative: (totals.actuals - totals.priorMonth) < 0\r\n          });\r\n        }\r\n      });\r\n    });\r\n\r\n    return { \r\n      tableData, \r\n      dateInfo: { \r\n        current: currentDate, \r\n        previousYear: previousYear, \r\n        previousMonth: previousMonth \r\n      } \r\n    };\r\n  };\r\n\r\n  // Transform the API data\r\n  const { tableData, dateInfo } = transformApiData(reportData);\r\n\r\n  // Fallback to empty state if no data\r\n  if (!reportData || !reportData.balanceSheetTableData || tableData.length === 0) {\r\n    return (\r\n      <div className=\"min-h-screen p-5\">\r\n        <div className=\"max-w-6xl bg-white p-10 mx-auto overflow-x-auto\">\r\n          <div>\r\n            <h2 style={{ ...subHeadingTextStyle, fontWeight: 'lighter', color: \"black\" }}>\r\n              Balance Sheet\r\n            </h2>\r\n            <div style={{ ...subHeadingTextStyle, fontWeight: 'lighter', color: \"black\" }}>\r\n              Balance Sheet Report\r\n            </div>\r\n            <div style={{ ...contentTextStyle, fontSize: \"20px\" }}>\r\n              Acme Print\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"flex items-center justify-center h-64\">\r\n            <div className=\"text-center\">\r\n              <div className=\"text-lg text-gray-600\">No data available</div>\r\n              <div className=\"text-sm text-gray-500 mt-2\">Please check your data source</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const renderTableRow = (item, index) => {\r\n    if (item.isMainCategory) {\r\n      return (\r\n        <tr key={index}>\r\n          <td className=\"text-left pl-2 font-bold text-gray-800\" style={{ ...contentTextStyle, fontSize: '15px' }}>\r\n            {item.category}\r\n          </td>\r\n          <td className=\"text-right\">&nbsp;</td>\r\n          <td className=\"text-right\">&nbsp;</td>\r\n          <td className=\"text-right\">&nbsp;</td>\r\n          <td className=\"text-right\">&nbsp;</td>\r\n          <td className=\"text-right\">&nbsp;</td>\r\n        </tr>\r\n      );\r\n    }\r\n\r\n    if (item.isSubCategory) {\r\n      return (\r\n        <tr key={index}>\r\n          <td className=\"text-left pl-4 font-semibold text-gray-700\" style={{ ...contentTextStyle, fontSize: '15px' }}>\r\n            {item.category}\r\n          </td>\r\n          <td className=\"text-right\">&nbsp;</td>\r\n          <td className=\"text-right\">&nbsp;</td>\r\n          <td className=\"text-right\">&nbsp;</td>\r\n          <td className=\"text-right\">&nbsp;</td>\r\n          <td className=\"text-right\">&nbsp;</td>\r\n        </tr>\r\n      );\r\n    }\r\n\r\n    if (item.isAccountGroup) {\r\n      return (\r\n        <tr key={index}>\r\n          <td className=\"text-left pl-6 font-medium text-gray-600\" style={{ ...contentTextStyle, fontSize: '15px' }}>\r\n            {item.category}\r\n          </td>\r\n          <td className=\"text-right\">&nbsp;</td>\r\n          <td className=\"text-right\">&nbsp;</td>\r\n          <td className=\"text-right\">&nbsp;</td>\r\n          <td className=\"text-right\">&nbsp;</td>\r\n          <td className=\"text-right\">&nbsp;</td>\r\n        </tr>\r\n      );\r\n    }\r\n\r\n    if (item.isGrandTotal) {\r\n      return (\r\n        <tr key={index} className=\"border-t-2 border-gray \">\r\n          <td className=\"text-left pl-2 font-bold text-gray-900\" style={{ ...contentTextStyle, fontSize: '15px' }}>\r\n            <strong>{item.label}</strong>\r\n          </td>\r\n          <td className=\"text-right font-mono font-bold\" style={{ ...contentTextStyle, fontSize: '15px' }}>\r\n            <strong>{item.jan25}</strong>\r\n          </td>\r\n          <td className=\"text-right font-mono font-bold\" style={{ ...contentTextStyle, fontSize: '15px' }}>\r\n            <strong>{item.jan24}</strong>\r\n          </td>\r\n          <td className=\"text-right font-mono font-bold\" style={{ ...contentTextStyle, fontSize: '15px' }}>\r\n            <strong>{item.jan24Percent}</strong>\r\n          </td>\r\n          <td className=\"text-right font-mono font-bold\" style={{ ...contentTextStyle, fontSize: '15px' }}>\r\n            <strong>{item.variance}</strong>\r\n          </td>\r\n          <td className=\"text-right font-bold\" style={{ ...contentTextStyle, fontSize: '15px' }}>\r\n            <strong>{item.variancePercent}</strong>\r\n          </td>\r\n        </tr>\r\n      );\r\n    }\r\n\r\n    if (item.isTotal) {\r\n      return (\r\n        <tr key={index} className=\"border-t-2 border-gray\">\r\n          <td className=\"text-left pl-6 font-semibold\" style={{ ...contentTextStyle, fontSize: '15px' }}>\r\n            <strong>{item.label}</strong>\r\n          </td>\r\n          <td className=\"text-right font-mono\" style={{ ...contentTextStyle, fontSize: '15px' }}>\r\n            <strong>{item.jan25}</strong>\r\n          </td>\r\n          <td className=\"text-right font-mono\" style={{ ...contentTextStyle, fontSize: '15px' }}>\r\n            <strong>{item.jan24}</strong>\r\n          </td>\r\n          <td className=\"text-right font-mono\" style={{ ...contentTextStyle, fontSize: '15px' }}>\r\n            <strong>{item.jan24Percent}</strong>\r\n          </td>\r\n          <td className=\"text-right font-mono\" style={{ ...contentTextStyle, fontSize: '15px' }}>\r\n            <strong>{item.variance}</strong>\r\n          </td>\r\n          <td \r\n            className={`text-right ${item.isNegative ? 'text-red-600' : ''}`}\r\n            style={{ ...contentTextStyle, fontSize: '15px' }}\r\n          >\r\n            <strong>{item.variancePercent}</strong>\r\n          </td>\r\n        </tr>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <tr key={index}>\r\n        <td className=\"text-left pl-8 font-normal\" style={{ ...contentTextStyle, fontSize: '15px' }}>\r\n          {item.label}\r\n        </td>\r\n        <td className=\"text-right font-mono\" style={{ ...contentTextStyle, fontSize: '15px' }}>\r\n          {item.jan25Percent}\r\n        </td>\r\n        <td className=\"text-right font-mono\" style={{ ...contentTextStyle, fontSize: '15px' }}>\r\n          {item.jan24}\r\n        </td>\r\n        <td className=\"text-right font-mono\" style={{ ...contentTextStyle, fontSize: '15px' }}>\r\n          {item.jan24Percent}\r\n        </td>\r\n        <td className=\"text-right font-mono\" style={{ ...contentTextStyle, fontSize: '15px' }}>\r\n          {item.variance}\r\n        </td>\r\n        <td \r\n          className={`text-right ${item.isNegative ? 'text-red-600' : ''}`}\r\n          style={{ ...contentTextStyle, fontSize: '15px' }}\r\n        >\r\n          {item.variancePercent}\r\n        </td>\r\n      </tr>\r\n    );\r\n  };\r\n\r\n  // Generate dynamic date description\r\n  const getDateDescription = () => {\r\n    if (dateInfo.current) {\r\n      const [month, year] = dateInfo.current.split(' ');\r\n      const monthNames = {\r\n        'Jan': 'January', 'Feb': 'February', 'Mar': 'March', 'Apr': 'April',\r\n        'May': 'May', 'Jun': 'June', 'Jul': 'July', 'Aug': 'August',\r\n        'Sep': 'September', 'Oct': 'October', 'Nov': 'November', 'Dec': 'December'\r\n      };\r\n      const fullMonth = monthNames[month] || month;\r\n      return `As of ${fullMonth} 31st, 20${year}`;\r\n    }\r\n    return 'As of January 31st, 2025';\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen p-5\">\r\n      {/* Main Container */}\r\n      <div className=\"max-w-6xl bg-white p-10 mx-auto overflow-x-auto\">\r\n        \r\n        {/* Header Section */}\r\n        <div>\r\n          <h2 \r\n            style={{ ...subHeadingTextStyle, fontWeight: 'lighter', color: \"black\" }}\r\n          >\r\n            Balance Sheet\r\n          </h2>\r\n          <div \r\n            style={{ ...subHeadingTextStyle, fontWeight: 'lighter', color: \"black\" }}\r\n          >\r\n            {getDateDescription()}\r\n          </div>\r\n          <div \r\n            style={{ ...contentTextStyle, fontSize: \"20px\" }}\r\n          >\r\n            Acme Print\r\n          </div>\r\n        </div>\r\n\r\n        {/* Table */}\r\n        <div className=\"overflow-x-auto\">\r\n          <table className=\"w-full border-collapse text-sm mt-4\">\r\n            <thead>\r\n              {/* Month Header Row */}\r\n              <tr className=\"text-black text-center bg-white\">\r\n                <th\r\n                  className=\"text-left bg-white border-0\"\r\n                  style={{ ...contentTextStyle, fontSize: '15px', color: 'black' }}\r\n                ></th>\r\n                <th\r\n                  className=\"text-left bg-white border-0 pl-10\"\r\n                  style={{ ...contentTextStyle, fontSize: '15px', color: 'black' }}\r\n                  colSpan=\"1\"\r\n                >\r\n                  {dateInfo.current}\r\n                </th>\r\n                <th\r\n                  className=\"text-left bg-white border-0 pl-10\"\r\n                  style={{ ...contentTextStyle, fontSize: '15px', color: 'black' }}\r\n                  colSpan=\"2\"\r\n                >\r\n                  {dateInfo.previousYear}\r\n                </th>\r\n                <th\r\n                  className=\"text-left bg-white border-0 pl-10\"\r\n                  style={{ ...contentTextStyle, fontSize: '15px', color: 'black' }}\r\n                  colSpan=\"2\"\r\n                >\r\n                  {dateInfo.previousMonth}\r\n                </th>\r\n              </tr>\r\n              {/* Column Header Row */}\r\n              <tr>\r\n                <th\r\n                  className=\"text-right text-white p-2 font-bold text-sm\"\r\n                  style={{\r\n                    ...contentTextStyle,\r\n                    fontSize: '15px',\r\n                    color: 'white'\r\n                  }}\r\n                ></th>\r\n                {/* Current Period */}\r\n                <th\r\n                  className=\"text-center text-white p-2 font-bold text-sm\"\r\n                  style={{\r\n                    backgroundColor: headerBgColor,\r\n                    ...contentTextStyle,\r\n                    fontSize: '15px',\r\n                    color: 'white',\r\n                    borderRight: '20px solid white'\r\n                  }}\r\n                >\r\n                  Actuals\r\n                </th>\r\n\r\n                {/* Prior Year Group */}\r\n                <th\r\n                  className=\"text-center text-white p-1 font-bold text-sm\"\r\n                  style={{\r\n                    backgroundColor: headerBgColor,\r\n                    ...contentTextStyle,\r\n                    fontSize: '15px',\r\n                    color: 'white'\r\n                  }}\r\n                >\r\n                  Prior Year\r\n                </th>\r\n                <th\r\n                  className=\"text-center text-white p-1 font-bold text-sm\"\r\n                  style={{\r\n                    backgroundColor: headerBgColor,\r\n                    ...contentTextStyle,\r\n                    fontSize: '15px',\r\n                    color: 'white',\r\n                    borderRight: '20px solid white'\r\n                  }}\r\n                >\r\n                  <div>Variance</div>\r\n                </th>\r\n\r\n                {/* Prior Month Group */}\r\n                <th\r\n                  className=\"text-center text-white p-2 font-bold text-sm\"\r\n                  style={{\r\n                    backgroundColor: headerBgColor,\r\n                    ...contentTextStyle,\r\n                    fontSize: '15px',\r\n                    color: 'white'\r\n                  }}\r\n                >\r\n                  Prior Month\r\n                </th>\r\n                <th\r\n                  className=\"text-center text-white p-2 font-bold text-sm\"\r\n                  style={{\r\n                    backgroundColor: headerBgColor,\r\n                    ...contentTextStyle,\r\n                    fontSize: '15px',\r\n                    color: 'white'\r\n                  }}\r\n                >\r\n                  Variance\r\n                </th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              {tableData.map((item, index) => renderTableRow(item, index))}\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n        <div className='text-center text-slate-300 text-xs border-b-4 border-blue-900 py-9'>\r\n          <p>\r\n            The information contained in this report is provided for informational purposes only and is not intended to substitute for obtaining accounting, tax, or financial advice from a professional accountant. Any tax advice\r\n            contained in this report is not intended to be used for the purpose of avoiding penalties under tax law. While we use reasonable efforts to furnish accurate and up-to-date information, we do not warrant that any\r\n            information contained in or made available through this report is accurate, complete, reliable, current or error-free. We assume no liability or responsibility for any errors or omissions in the content of this report or\r\n            delivered information.\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n            \r\nexport default BalanceSheetDashboard;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,qBAAqB,GAAGA,CAAC;EAC7BC,eAAe,GAAG,CAAC,CAAC;EACpBC,mBAAmB,GAAG,CAAC,CAAC;EACxBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,UAAU,GAAG,IAAI,CAAC;AACpB,CAAC,KAAK;EACJ;EACA,MAAMC,aAAa,GAAGJ,eAAe,CAACK,KAAK,IAAI,SAAS;;EAExD;EACA,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;IACpC,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACC,qBAAqB,EAAE;MAC9C,OAAO;QACLC,SAAS,EAAE,EAAE;QACbC,QAAQ,EAAE;UACRC,OAAO,EAAE,QAAQ;UACjBC,YAAY,EAAE,QAAQ;UACtBC,aAAa,EAAE;QACjB;MACF,CAAC;IACH;IAEA,MAAMC,OAAO,GAAGP,OAAO,CAACC,qBAAqB;;IAE7C;IACA,IAAIO,WAAW,GAAG,QAAQ;IAC1B,IAAIH,YAAY,GAAG,QAAQ;IAC3B,IAAIC,aAAa,GAAG,QAAQ;IAE5B,IAAIC,OAAO,CAACE,MAAM,GAAG,CAAC,EAAE;MACtB,MAAMC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACH,OAAO,CAAC,CAAC,CAAC,CAAC;;MAEpC;MACA,MAAMK,UAAU,GAAGF,IAAI,CAACG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,QAAQ,CAAC,UAAU,CAAC,CAAC;MAC7D,IAAIH,UAAU,EAAE;QACd,MAAMI,SAAS,GAAGJ,UAAU,CAACK,KAAK,CAAC,yBAAyB,CAAC;QAC7D,IAAID,SAAS,EAAE;UACbR,WAAW,GAAGQ,SAAS,CAAC,CAAC,CAAC,CAACE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;;UAE5C;UACA,MAAM,CAACC,KAAK,EAAEC,IAAI,CAAC,GAAGZ,WAAW,CAACa,KAAK,CAAC,GAAG,CAAC;UAC5ChB,YAAY,GAAG,GAAGc,KAAK,IAAIG,QAAQ,CAACF,IAAI,CAAC,GAAG,CAAC,EAAE;QACjD;MACF;;MAEA;MACA,MAAMG,aAAa,GAAGb,IAAI,CAACG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,QAAQ,CAAC,cAAc,CAAC,CAAC;MACpE,IAAIQ,aAAa,EAAE;QACjB,MAAMC,UAAU,GAAGD,aAAa,CAACN,KAAK,CAAC,6BAA6B,CAAC;QACrE,IAAIO,UAAU,EAAE;UACdlB,aAAa,GAAGkB,UAAU,CAAC,CAAC,CAAC,CAACN,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;QACjD;MACF;IACF;;IAEA;IACA,MAAMO,oBAAoB,GAAGlB,OAAO,CAACmB,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;MACzD,MAAMC,cAAc,GAAGD,IAAI,CAACE,qBAAqB;MACjD,MAAMC,WAAW,GAAGH,IAAI,CAACI,YAAY;MAErC,IAAI,CAACL,GAAG,CAACE,cAAc,CAAC,EAAE;QACxBF,GAAG,CAACE,cAAc,CAAC,GAAG,CAAC,CAAC;MAC1B;MACA,IAAI,CAACF,GAAG,CAACE,cAAc,CAAC,CAACE,WAAW,CAAC,EAAE;QACrCJ,GAAG,CAACE,cAAc,CAAC,CAACE,WAAW,CAAC,GAAG,EAAE;MACvC;MACAJ,GAAG,CAACE,cAAc,CAAC,CAACE,WAAW,CAAC,CAACE,IAAI,CAACL,IAAI,CAAC;MAC3C,OAAOD,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEN;IACA,MAAMzB,SAAS,GAAG,EAAE;IAEpBS,MAAM,CAACD,IAAI,CAACe,oBAAoB,CAAC,CAACS,OAAO,CAACL,cAAc,IAAI;MAC1D;MACA3B,SAAS,CAAC+B,IAAI,CAAC;QACbE,cAAc,EAAE,IAAI;QACpBC,QAAQ,EAAEP;MACZ,CAAC,CAAC;MAEF,MAAMQ,YAAY,GAAGZ,oBAAoB,CAACI,cAAc,CAAC;MAEzDlB,MAAM,CAACD,IAAI,CAAC2B,YAAY,CAAC,CAACH,OAAO,CAACH,WAAW,IAAI;QAC/C;QACA7B,SAAS,CAAC+B,IAAI,CAAC;UACbK,aAAa,EAAE,IAAI;UACnBF,QAAQ,EAAEL;QACZ,CAAC,CAAC;;QAEF;QACAM,YAAY,CAACN,WAAW,CAAC,CAACG,OAAO,CAACN,IAAI,IAAI;UACxC;UACA,MAAMW,OAAO,GAAGX,IAAI,CAACY,YAAY,CAACC,WAAW,CAAC,CAAC,CAAC1B,QAAQ,CAAC,OAAO,CAAC;;UAEjE;UACA,MAAM2B,iBAAiB,GAAGC,UAAU,CAACf,IAAI,CAACgB,mBAAmB,IAAI,GAAG,CAAC;UACrE,MAAMC,kBAAkB,GAAGF,UAAU,CAACf,IAAI,CAACkB,oBAAoB,IAAI,GAAG,CAAC;UACvE,MAAMC,mBAAmB,GAAGL,iBAAiB,GAAG,CAAC;UACjD,MAAMM,oBAAoB,GAAGH,kBAAkB,GAAG,CAAC;UAEnD3C,SAAS,CAAC+B,IAAI,CAAC;YACbgB,KAAK,EAAErB,IAAI,CAACY,YAAY;YACxBU,KAAK,EAAEtB,IAAI,CAACuB,cAAc,IAAI,GAAG;YAAE;YACnCC,YAAY,EAAExB,IAAI,CAACuB,cAAc,IAAI,GAAG;YAAE;YAC1CE,KAAK,EAAEzB,IAAI,CAAC0B,iBAAiB,IAAI,GAAG;YAAE;YACtCC,YAAY,EAAE3B,IAAI,CAACgB,mBAAmB,IAAI,GAAG;YAAE;YAC/CY,QAAQ,EAAE5B,IAAI,CAAC6B,kBAAkB,IAAI,GAAG;YAAE;YAC1CC,eAAe,EAAE9B,IAAI,CAACkB,oBAAoB,IAAI,GAAG;YAAE;YACnDP,OAAO,EAAEA,OAAO;YAChBoB,YAAY,EAAE/B,IAAI,CAACY,YAAY,CAACC,WAAW,CAAC,CAAC,CAAC1B,QAAQ,CAAC,OAAO,CAAC,KACjDc,cAAc,CAACY,WAAW,CAAC,CAAC,CAAC1B,QAAQ,CAAC,OAAO,CAAC,IAC9Cc,cAAc,CAACY,WAAW,CAAC,CAAC,CAAC1B,QAAQ,CAAC,OAAO,CAAC,CAAC;YAC7D6C,UAAU,EAAEZ,oBAAoB,CAAC;UACnC,CAAC,CAAC;QACJ,CAAC,CAAC;;QAEF;QACA,MAAMa,QAAQ,GAAGxB,YAAY,CAACN,WAAW,CAAC,CAAClB,IAAI,CAACe,IAAI,IAClDA,IAAI,CAACY,YAAY,CAACC,WAAW,CAAC,CAAC,CAAC1B,QAAQ,CAAC,OAAO,CAClD,CAAC;QAED,IAAI,CAAC8C,QAAQ,IAAIxB,YAAY,CAACN,WAAW,CAAC,CAACtB,MAAM,GAAG,CAAC,EAAE;UACrD;UACA,MAAMqD,MAAM,GAAGzB,YAAY,CAACN,WAAW,CAAC,CAACL,MAAM,CAAC,CAACqC,GAAG,EAAEnC,IAAI,KAAK;YAC7D,IAAI,CAACA,IAAI,CAACY,YAAY,CAACC,WAAW,CAAC,CAAC,CAAC1B,QAAQ,CAAC,OAAO,CAAC,EAAE;cACtDgD,GAAG,CAACC,OAAO,IAAIrB,UAAU,CAACf,IAAI,CAACuB,cAAc,IAAI,GAAG,CAAC;cACrDY,GAAG,CAACE,SAAS,IAAItB,UAAU,CAACf,IAAI,CAAC0B,iBAAiB,IAAI,GAAG,CAAC;cAC1DS,GAAG,CAACG,UAAU,IAAIvB,UAAU,CAACf,IAAI,CAAC6B,kBAAkB,IAAI,GAAG,CAAC;YAC9D;YACA,OAAOM,GAAG;UACZ,CAAC,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,SAAS,EAAE,CAAC;YAAEC,UAAU,EAAE;UAAE,CAAC,CAAC;UAE/ChE,SAAS,CAAC+B,IAAI,CAAC;YACbgB,KAAK,EAAE,SAASlB,WAAW,EAAE;YAC7BmB,KAAK,EAAEY,MAAM,CAACE,OAAO,CAACG,QAAQ,CAAC,CAAC;YAChCf,YAAY,EAAEU,MAAM,CAACE,OAAO,CAACG,QAAQ,CAAC,CAAC;YACvCd,KAAK,EAAES,MAAM,CAACG,SAAS,CAACE,QAAQ,CAAC,CAAC;YAClCZ,YAAY,EAAE,CAACO,MAAM,CAACE,OAAO,GAAGF,MAAM,CAACG,SAAS,EAAEE,QAAQ,CAAC,CAAC;YAC5DX,QAAQ,EAAEM,MAAM,CAACI,UAAU,CAACC,QAAQ,CAAC,CAAC;YACtCT,eAAe,EAAE,CAACI,MAAM,CAACE,OAAO,GAAGF,MAAM,CAACI,UAAU,EAAEC,QAAQ,CAAC,CAAC;YAChE5B,OAAO,EAAE,IAAI;YACbqB,UAAU,EAAGE,MAAM,CAACE,OAAO,GAAGF,MAAM,CAACI,UAAU,GAAI;UACrD,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO;MACLhE,SAAS;MACTC,QAAQ,EAAE;QACRC,OAAO,EAAEI,WAAW;QACpBH,YAAY,EAAEA,YAAY;QAC1BC,aAAa,EAAEA;MACjB;IACF,CAAC;EACH,CAAC;;EAED;EACA,MAAM;IAAEJ,SAAS;IAAEC;EAAS,CAAC,GAAGJ,gBAAgB,CAACH,UAAU,CAAC;;EAE5D;EACA,IAAI,CAACA,UAAU,IAAI,CAACA,UAAU,CAACK,qBAAqB,IAAIC,SAAS,CAACO,MAAM,KAAK,CAAC,EAAE;IAC9E,oBACElB,OAAA;MAAK6E,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/B9E,OAAA;QAAK6E,SAAS,EAAC,iDAAiD;QAAAC,QAAA,gBAC9D9E,OAAA;UAAA8E,QAAA,gBACE9E,OAAA;YAAI+E,KAAK,EAAE;cAAE,GAAG5E,mBAAmB;cAAE6E,UAAU,EAAE,SAAS;cAAEzE,KAAK,EAAE;YAAQ,CAAE;YAAAuE,QAAA,EAAC;UAE9E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLpF,OAAA;YAAK+E,KAAK,EAAE;cAAE,GAAG5E,mBAAmB;cAAE6E,UAAU,EAAE,SAAS;cAAEzE,KAAK,EAAE;YAAQ,CAAE;YAAAuE,QAAA,EAAC;UAE/E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNpF,OAAA;YAAK+E,KAAK,EAAE;cAAE,GAAG3E,gBAAgB;cAAEiF,QAAQ,EAAE;YAAO,CAAE;YAAAP,QAAA,EAAC;UAEvD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENpF,OAAA;UAAK6E,SAAS,EAAC,uCAAuC;UAAAC,QAAA,eACpD9E,OAAA;YAAK6E,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B9E,OAAA;cAAK6E,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9DpF,OAAA;cAAK6E,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAA6B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5E;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAME,cAAc,GAAGA,CAACjD,IAAI,EAAEkD,KAAK,KAAK;IACtC,IAAIlD,IAAI,CAACO,cAAc,EAAE;MACvB,oBACE5C,OAAA;QAAA8E,QAAA,gBACE9E,OAAA;UAAI6E,SAAS,EAAC,wCAAwC;UAACE,KAAK,EAAE;YAAE,GAAG3E,gBAAgB;YAAEiF,QAAQ,EAAE;UAAO,CAAE;UAAAP,QAAA,EACrGzC,IAAI,CAACQ;QAAQ;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACLpF,OAAA;UAAI6E,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtCpF,OAAA;UAAI6E,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtCpF,OAAA;UAAI6E,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtCpF,OAAA;UAAI6E,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtCpF,OAAA;UAAI6E,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA,GAR/BG,KAAK;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OASV,CAAC;IAET;IAEA,IAAI/C,IAAI,CAACU,aAAa,EAAE;MACtB,oBACE/C,OAAA;QAAA8E,QAAA,gBACE9E,OAAA;UAAI6E,SAAS,EAAC,4CAA4C;UAACE,KAAK,EAAE;YAAE,GAAG3E,gBAAgB;YAAEiF,QAAQ,EAAE;UAAO,CAAE;UAAAP,QAAA,EACzGzC,IAAI,CAACQ;QAAQ;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACLpF,OAAA;UAAI6E,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtCpF,OAAA;UAAI6E,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtCpF,OAAA;UAAI6E,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtCpF,OAAA;UAAI6E,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtCpF,OAAA;UAAI6E,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA,GAR/BG,KAAK;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OASV,CAAC;IAET;IAEA,IAAI/C,IAAI,CAACmD,cAAc,EAAE;MACvB,oBACExF,OAAA;QAAA8E,QAAA,gBACE9E,OAAA;UAAI6E,SAAS,EAAC,0CAA0C;UAACE,KAAK,EAAE;YAAE,GAAG3E,gBAAgB;YAAEiF,QAAQ,EAAE;UAAO,CAAE;UAAAP,QAAA,EACvGzC,IAAI,CAACQ;QAAQ;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACLpF,OAAA;UAAI6E,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtCpF,OAAA;UAAI6E,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtCpF,OAAA;UAAI6E,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtCpF,OAAA;UAAI6E,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtCpF,OAAA;UAAI6E,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA,GAR/BG,KAAK;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OASV,CAAC;IAET;IAEA,IAAI/C,IAAI,CAAC+B,YAAY,EAAE;MACrB,oBACEpE,OAAA;QAAgB6E,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACjD9E,OAAA;UAAI6E,SAAS,EAAC,wCAAwC;UAACE,KAAK,EAAE;YAAE,GAAG3E,gBAAgB;YAAEiF,QAAQ,EAAE;UAAO,CAAE;UAAAP,QAAA,eACtG9E,OAAA;YAAA8E,QAAA,EAASzC,IAAI,CAACqB;UAAK;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACLpF,OAAA;UAAI6E,SAAS,EAAC,gCAAgC;UAACE,KAAK,EAAE;YAAE,GAAG3E,gBAAgB;YAAEiF,QAAQ,EAAE;UAAO,CAAE;UAAAP,QAAA,eAC9F9E,OAAA;YAAA8E,QAAA,EAASzC,IAAI,CAACsB;UAAK;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACLpF,OAAA;UAAI6E,SAAS,EAAC,gCAAgC;UAACE,KAAK,EAAE;YAAE,GAAG3E,gBAAgB;YAAEiF,QAAQ,EAAE;UAAO,CAAE;UAAAP,QAAA,eAC9F9E,OAAA;YAAA8E,QAAA,EAASzC,IAAI,CAACyB;UAAK;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACLpF,OAAA;UAAI6E,SAAS,EAAC,gCAAgC;UAACE,KAAK,EAAE;YAAE,GAAG3E,gBAAgB;YAAEiF,QAAQ,EAAE;UAAO,CAAE;UAAAP,QAAA,eAC9F9E,OAAA;YAAA8E,QAAA,EAASzC,IAAI,CAAC2B;UAAY;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eACLpF,OAAA;UAAI6E,SAAS,EAAC,gCAAgC;UAACE,KAAK,EAAE;YAAE,GAAG3E,gBAAgB;YAAEiF,QAAQ,EAAE;UAAO,CAAE;UAAAP,QAAA,eAC9F9E,OAAA;YAAA8E,QAAA,EAASzC,IAAI,CAAC4B;UAAQ;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACLpF,OAAA;UAAI6E,SAAS,EAAC,sBAAsB;UAACE,KAAK,EAAE;YAAE,GAAG3E,gBAAgB;YAAEiF,QAAQ,EAAE;UAAO,CAAE;UAAAP,QAAA,eACpF9E,OAAA;YAAA8E,QAAA,EAASzC,IAAI,CAAC8B;UAAe;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC;MAAA,GAlBEG,KAAK;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAmBV,CAAC;IAET;IAEA,IAAI/C,IAAI,CAACW,OAAO,EAAE;MAChB,oBACEhD,OAAA;QAAgB6E,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBAChD9E,OAAA;UAAI6E,SAAS,EAAC,8BAA8B;UAACE,KAAK,EAAE;YAAE,GAAG3E,gBAAgB;YAAEiF,QAAQ,EAAE;UAAO,CAAE;UAAAP,QAAA,eAC5F9E,OAAA;YAAA8E,QAAA,EAASzC,IAAI,CAACqB;UAAK;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACLpF,OAAA;UAAI6E,SAAS,EAAC,sBAAsB;UAACE,KAAK,EAAE;YAAE,GAAG3E,gBAAgB;YAAEiF,QAAQ,EAAE;UAAO,CAAE;UAAAP,QAAA,eACpF9E,OAAA;YAAA8E,QAAA,EAASzC,IAAI,CAACsB;UAAK;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACLpF,OAAA;UAAI6E,SAAS,EAAC,sBAAsB;UAACE,KAAK,EAAE;YAAE,GAAG3E,gBAAgB;YAAEiF,QAAQ,EAAE;UAAO,CAAE;UAAAP,QAAA,eACpF9E,OAAA;YAAA8E,QAAA,EAASzC,IAAI,CAACyB;UAAK;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACLpF,OAAA;UAAI6E,SAAS,EAAC,sBAAsB;UAACE,KAAK,EAAE;YAAE,GAAG3E,gBAAgB;YAAEiF,QAAQ,EAAE;UAAO,CAAE;UAAAP,QAAA,eACpF9E,OAAA;YAAA8E,QAAA,EAASzC,IAAI,CAAC2B;UAAY;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eACLpF,OAAA;UAAI6E,SAAS,EAAC,sBAAsB;UAACE,KAAK,EAAE;YAAE,GAAG3E,gBAAgB;YAAEiF,QAAQ,EAAE;UAAO,CAAE;UAAAP,QAAA,eACpF9E,OAAA;YAAA8E,QAAA,EAASzC,IAAI,CAAC4B;UAAQ;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACLpF,OAAA;UACE6E,SAAS,EAAE,cAAcxC,IAAI,CAACgC,UAAU,GAAG,cAAc,GAAG,EAAE,EAAG;UACjEU,KAAK,EAAE;YAAE,GAAG3E,gBAAgB;YAAEiF,QAAQ,EAAE;UAAO,CAAE;UAAAP,QAAA,eAEjD9E,OAAA;YAAA8E,QAAA,EAASzC,IAAI,CAAC8B;UAAe;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC;MAAA,GArBEG,KAAK;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAsBV,CAAC;IAET;IAEA,oBACEpF,OAAA;MAAA8E,QAAA,gBACE9E,OAAA;QAAI6E,SAAS,EAAC,4BAA4B;QAACE,KAAK,EAAE;UAAE,GAAG3E,gBAAgB;UAAEiF,QAAQ,EAAE;QAAO,CAAE;QAAAP,QAAA,EACzFzC,IAAI,CAACqB;MAAK;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACLpF,OAAA;QAAI6E,SAAS,EAAC,sBAAsB;QAACE,KAAK,EAAE;UAAE,GAAG3E,gBAAgB;UAAEiF,QAAQ,EAAE;QAAO,CAAE;QAAAP,QAAA,EACnFzC,IAAI,CAACwB;MAAY;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eACLpF,OAAA;QAAI6E,SAAS,EAAC,sBAAsB;QAACE,KAAK,EAAE;UAAE,GAAG3E,gBAAgB;UAAEiF,QAAQ,EAAE;QAAO,CAAE;QAAAP,QAAA,EACnFzC,IAAI,CAACyB;MAAK;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACLpF,OAAA;QAAI6E,SAAS,EAAC,sBAAsB;QAACE,KAAK,EAAE;UAAE,GAAG3E,gBAAgB;UAAEiF,QAAQ,EAAE;QAAO,CAAE;QAAAP,QAAA,EACnFzC,IAAI,CAAC2B;MAAY;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eACLpF,OAAA;QAAI6E,SAAS,EAAC,sBAAsB;QAACE,KAAK,EAAE;UAAE,GAAG3E,gBAAgB;UAAEiF,QAAQ,EAAE;QAAO,CAAE;QAAAP,QAAA,EACnFzC,IAAI,CAAC4B;MAAQ;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eACLpF,OAAA;QACE6E,SAAS,EAAE,cAAcxC,IAAI,CAACgC,UAAU,GAAG,cAAc,GAAG,EAAE,EAAG;QACjEU,KAAK,EAAE;UAAE,GAAG3E,gBAAgB;UAAEiF,QAAQ,EAAE;QAAO,CAAE;QAAAP,QAAA,EAEhDzC,IAAI,CAAC8B;MAAe;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA,GArBEG,KAAK;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAsBV,CAAC;EAET,CAAC;;EAED;EACA,MAAMK,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI7E,QAAQ,CAACC,OAAO,EAAE;MACpB,MAAM,CAACe,KAAK,EAAEC,IAAI,CAAC,GAAGjB,QAAQ,CAACC,OAAO,CAACiB,KAAK,CAAC,GAAG,CAAC;MACjD,MAAM4D,UAAU,GAAG;QACjB,KAAK,EAAE,SAAS;QAAE,KAAK,EAAE,UAAU;QAAE,KAAK,EAAE,OAAO;QAAE,KAAK,EAAE,OAAO;QACnE,KAAK,EAAE,KAAK;QAAE,KAAK,EAAE,MAAM;QAAE,KAAK,EAAE,MAAM;QAAE,KAAK,EAAE,QAAQ;QAC3D,KAAK,EAAE,WAAW;QAAE,KAAK,EAAE,SAAS;QAAE,KAAK,EAAE,UAAU;QAAE,KAAK,EAAE;MAClE,CAAC;MACD,MAAMC,SAAS,GAAGD,UAAU,CAAC9D,KAAK,CAAC,IAAIA,KAAK;MAC5C,OAAO,SAAS+D,SAAS,YAAY9D,IAAI,EAAE;IAC7C;IACA,OAAO,0BAA0B;EACnC,CAAC;EAED,oBACE7B,OAAA;IAAK6E,SAAS,EAAC,kBAAkB;IAAAC,QAAA,eAE/B9E,OAAA;MAAK6E,SAAS,EAAC,iDAAiD;MAAAC,QAAA,gBAG9D9E,OAAA;QAAA8E,QAAA,gBACE9E,OAAA;UACE+E,KAAK,EAAE;YAAE,GAAG5E,mBAAmB;YAAE6E,UAAU,EAAE,SAAS;YAAEzE,KAAK,EAAE;UAAQ,CAAE;UAAAuE,QAAA,EAC1E;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLpF,OAAA;UACE+E,KAAK,EAAE;YAAE,GAAG5E,mBAAmB;YAAE6E,UAAU,EAAE,SAAS;YAAEzE,KAAK,EAAE;UAAQ,CAAE;UAAAuE,QAAA,EAExEW,kBAAkB,CAAC;QAAC;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACNpF,OAAA;UACE+E,KAAK,EAAE;YAAE,GAAG3E,gBAAgB;YAAEiF,QAAQ,EAAE;UAAO,CAAE;UAAAP,QAAA,EAClD;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNpF,OAAA;QAAK6E,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9B9E,OAAA;UAAO6E,SAAS,EAAC,qCAAqC;UAAAC,QAAA,gBACpD9E,OAAA;YAAA8E,QAAA,gBAEE9E,OAAA;cAAI6E,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC7C9E,OAAA;gBACE6E,SAAS,EAAC,6BAA6B;gBACvCE,KAAK,EAAE;kBAAE,GAAG3E,gBAAgB;kBAAEiF,QAAQ,EAAE,MAAM;kBAAE9E,KAAK,EAAE;gBAAQ;cAAE;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACNpF,OAAA;gBACE6E,SAAS,EAAC,mCAAmC;gBAC7CE,KAAK,EAAE;kBAAE,GAAG3E,gBAAgB;kBAAEiF,QAAQ,EAAE,MAAM;kBAAE9E,KAAK,EAAE;gBAAQ,CAAE;gBACjEqF,OAAO,EAAC,GAAG;gBAAAd,QAAA,EAEVlE,QAAQ,CAACC;cAAO;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACLpF,OAAA;gBACE6E,SAAS,EAAC,mCAAmC;gBAC7CE,KAAK,EAAE;kBAAE,GAAG3E,gBAAgB;kBAAEiF,QAAQ,EAAE,MAAM;kBAAE9E,KAAK,EAAE;gBAAQ,CAAE;gBACjEqF,OAAO,EAAC,GAAG;gBAAAd,QAAA,EAEVlE,QAAQ,CAACE;cAAY;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACLpF,OAAA;gBACE6E,SAAS,EAAC,mCAAmC;gBAC7CE,KAAK,EAAE;kBAAE,GAAG3E,gBAAgB;kBAAEiF,QAAQ,EAAE,MAAM;kBAAE9E,KAAK,EAAE;gBAAQ,CAAE;gBACjEqF,OAAO,EAAC,GAAG;gBAAAd,QAAA,EAEVlE,QAAQ,CAACG;cAAa;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAELpF,OAAA;cAAA8E,QAAA,gBACE9E,OAAA;gBACE6E,SAAS,EAAC,6CAA6C;gBACvDE,KAAK,EAAE;kBACL,GAAG3E,gBAAgB;kBACnBiF,QAAQ,EAAE,MAAM;kBAChB9E,KAAK,EAAE;gBACT;cAAE;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENpF,OAAA;gBACE6E,SAAS,EAAC,8CAA8C;gBACxDE,KAAK,EAAE;kBACLc,eAAe,EAAEvF,aAAa;kBAC9B,GAAGF,gBAAgB;kBACnBiF,QAAQ,EAAE,MAAM;kBAChB9E,KAAK,EAAE,OAAO;kBACduF,WAAW,EAAE;gBACf,CAAE;gBAAAhB,QAAA,EACH;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAGLpF,OAAA;gBACE6E,SAAS,EAAC,8CAA8C;gBACxDE,KAAK,EAAE;kBACLc,eAAe,EAAEvF,aAAa;kBAC9B,GAAGF,gBAAgB;kBACnBiF,QAAQ,EAAE,MAAM;kBAChB9E,KAAK,EAAE;gBACT,CAAE;gBAAAuE,QAAA,EACH;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLpF,OAAA;gBACE6E,SAAS,EAAC,8CAA8C;gBACxDE,KAAK,EAAE;kBACLc,eAAe,EAAEvF,aAAa;kBAC9B,GAAGF,gBAAgB;kBACnBiF,QAAQ,EAAE,MAAM;kBAChB9E,KAAK,EAAE,OAAO;kBACduF,WAAW,EAAE;gBACf,CAAE;gBAAAhB,QAAA,eAEF9E,OAAA;kBAAA8E,QAAA,EAAK;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eAGLpF,OAAA;gBACE6E,SAAS,EAAC,8CAA8C;gBACxDE,KAAK,EAAE;kBACLc,eAAe,EAAEvF,aAAa;kBAC9B,GAAGF,gBAAgB;kBACnBiF,QAAQ,EAAE,MAAM;kBAChB9E,KAAK,EAAE;gBACT,CAAE;gBAAAuE,QAAA,EACH;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLpF,OAAA;gBACE6E,SAAS,EAAC,8CAA8C;gBACxDE,KAAK,EAAE;kBACLc,eAAe,EAAEvF,aAAa;kBAC9B,GAAGF,gBAAgB;kBACnBiF,QAAQ,EAAE,MAAM;kBAChB9E,KAAK,EAAE;gBACT,CAAE;gBAAAuE,QAAA,EACH;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRpF,OAAA;YAAA8E,QAAA,EACGnE,SAAS,CAACoF,GAAG,CAAC,CAAC1D,IAAI,EAAEkD,KAAK,KAAKD,cAAc,CAACjD,IAAI,EAAEkD,KAAK,CAAC;UAAC;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNpF,OAAA;QAAK6E,SAAS,EAAC,oEAAoE;QAAAC,QAAA,eACjF9E,OAAA;UAAA8E,QAAA,EAAG;QAKH;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACY,EAAA,GA3dI/F,qBAAqB;AA6d3B,eAAeA,qBAAqB;AAAC,IAAA+F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}