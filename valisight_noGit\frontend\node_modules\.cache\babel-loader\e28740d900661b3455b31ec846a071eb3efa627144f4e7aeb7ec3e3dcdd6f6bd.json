{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JASON_NEW\\\\valisight_noGit\\\\frontend\\\\src\\\\pages\\\\reports\\\\ReportPages\\\\MonthTrailing.jsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfitLoss13MonthDashboard = ({\n  headerTextStyle = {},\n  headingTextStyle = {},\n  subHeadingTextStyle = {},\n  subHeaderTextStyle = {},\n  contentTextStyle = {},\n  reportData = null // Add reportData prop to receive API data\n}) => {\n  // Extract background color from headerTextStyle for table header\n  const headerBgColor = headerTextStyle.color || '#20b2aa';\n\n  // Function to transform API data into the required format\n  const transformApiData = apiData => {\n    if (!apiData || !apiData.profitAndLossMonthsTrailing) {\n      return {\n        months: [],\n        tableData: []\n      };\n    }\n    const rawData = apiData.profitAndLossMonthsTrailing;\n\n    // Extract months from the first data item (excluding account_type and account_name)\n    const months = rawData.length > 0 ? Object.keys(rawData[0]).filter(key => key !== 'account_type' && key !== 'account_name') : [];\n\n    // Group data by account_type\n    const groupedData = rawData.reduce((acc, item) => {\n      const accountType = item.account_type;\n      if (!acc[accountType]) {\n        acc[accountType] = [];\n      }\n      acc[accountType].push(item);\n      return acc;\n    }, {});\n\n    // Transform grouped data into table format\n    const tableData = [];\n    Object.keys(groupedData).forEach(accountType => {\n      // Add header row for account type\n      tableData.push({\n        isHeader: true,\n        category: accountType\n      });\n\n      // Add data rows for this account type\n      groupedData[accountType].forEach(item => {\n        const rowData = months.map(month => item[month] || '0');\n\n        // Check if this is a total row (contains \"Total\" in account_name)\n        const isTotal = item.account_name.toLowerCase().includes('total');\n        tableData.push({\n          label: item.account_name,\n          data: rowData,\n          isTotal: isTotal,\n          indented: !isTotal && !item.account_name.toLowerCase().includes('total')\n        });\n      });\n    });\n    return {\n      months,\n      tableData\n    };\n  };\n\n  // Transform the API data\n  const {\n    months,\n    tableData\n  } = transformApiData(reportData);\n\n  // Fallback to empty state if no data\n  if (!reportData || !reportData.profitAndLossMonthsTrailing || months.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-5 section profit-loss-section month-trailing\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-8xl mx-auto bg-white p-10 mx-auto overflow-x-auto   report-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"report-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: 'lighter',\n              color: \"black\"\n            },\n            children: \"Profit and Loss\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: 'lighter',\n              color: \"black\"\n            },\n            children: \"13 Month Trailing\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              ...contentTextStyle,\n              fontSize: \"20px\"\n            },\n            children: \"Acme Print\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center h-64\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg text-gray-600\",\n              children: \"No data available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-500 mt-2\",\n              children: \"Please check your data source\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this);\n  }\n  const renderTableRow = (item, index) => {\n    if (item.isHeader) {\n      return /*#__PURE__*/_jsxDEV(\"tr\", {\n        className: \"font-bold text-gray-800\",\n        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-left pl-2 font-normal\",\n          style: {\n            ...contentTextStyle,\n            fontSize: \"15px\",\n            fontWeight: 'light'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: item.category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this), months.map((_, i) => /*#__PURE__*/_jsxDEV(\"td\", {\n          style: {\n            backgroundColor: i === months.length - 1 ? '#d2e9ea' : 'transparent'\n          }\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 13\n        }, this))]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this);\n    }\n    if (item.isTotal) {\n      return /*#__PURE__*/_jsxDEV(\"tr\", {\n        className: \"border-t-2 border-gray\",\n        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-left pl-2 font-normal\",\n          style: {\n            ...contentTextStyle,\n            fontSize: \"15px\",\n            fontWeight: 'light'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), item.data.map((value, i) => /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right font-mono\",\n          style: {\n            ...contentTextStyle,\n            backgroundColor: i === item.data.length - 1 ? '#d2e9ea' : 'transparent'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 15\n          }, this)\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 13\n        }, this))]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"tr\", {\n      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n        className: `text-left font-normal ${item.indented ? 'pl-6' : 'pl-2'}`,\n        style: {\n          ...contentTextStyle,\n          fontSize: \"15px\",\n          fontWeight: 'light'\n        },\n        children: item.label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this), item.data.map((value, i) => /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"text-right font-mono\",\n        style: {\n          ...contentTextStyle,\n          backgroundColor: i === item.data.length - 1 ? '#d2e9ea' : 'transparent'\n        },\n        children: value\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 11\n      }, this))]\n    }, index, true, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-5 section profit-loss-section\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-8xl mx-auto bg-white p-10 mx-auto overflow-x-auto  report-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"report-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          style: {\n            ...subHeadingTextStyle,\n            fontWeight: 'lighter',\n            color: \"black\"\n          },\n          children: \"Profit and Loss\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...subHeadingTextStyle,\n            fontWeight: 'lighter',\n            color: \"black\"\n          },\n          children: \"13 Month Trailing\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...contentTextStyle,\n            fontSize: \"20px\"\n          },\n          children: \"Acme Print\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"w-full border-collapse text-sm mt-4 profit-loss-table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"table-header-group\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"header-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"month-header\",\n                style: {\n                  width: '200px',\n                  backgroundColor: headerBgColor,\n                  ...contentTextStyle,\n                  color: 'white'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this), months.map((month, index) => /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-right text-white p-2 font-bold text-sm month-header\",\n                style: {\n                  backgroundColor: headerBgColor,\n                  ...contentTextStyle,\n                  color: 'white',\n                  fontSize: '15px'\n                },\n                children: month\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"table-row-group\",\n            children: tableData.map((item, index) => renderTableRow(item, `data-${index}`))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center text-slate-300 text-xs border-b-4 border-blue-900 py-9\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"The information contained in this report is provided for informational purposes only and is not intended to substitute for obtaining accounting, tax, or financial advice from a professional accountant. Any tax advice contained in this report is not intended to be used for the purpose of avoiding penalties under tax law. While we use reasonable efforts to furnish accurate and up-to-date information, we do not warrant that any information contained in or made available through this report is accurate, complete, reliable, current or error-free. We assume no liability or responsibility for any errors or omissions in the content of this report or delivered information.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 163,\n    columnNumber: 5\n  }, this);\n};\n_c = ProfitLoss13MonthDashboard;\nexport default ProfitLoss13MonthDashboard;\nvar _c;\n$RefreshReg$(_c, \"ProfitLoss13MonthDashboard\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ProfitLoss13MonthDashboard", "headerTextStyle", "headingTextStyle", "subHeadingTextStyle", "subHeaderTextStyle", "contentTextStyle", "reportData", "headerBgColor", "color", "transformApiData", "apiData", "profitAndLossMonthsTrailing", "months", "tableData", "rawData", "length", "Object", "keys", "filter", "key", "groupedData", "reduce", "acc", "item", "accountType", "account_type", "push", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>", "category", "rowData", "map", "month", "isTotal", "account_name", "toLowerCase", "includes", "label", "data", "indented", "className", "children", "style", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "renderTableRow", "index", "_", "i", "backgroundColor", "value", "width", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/JASON_NEW/valisight_noGit/frontend/src/pages/reports/ReportPages/MonthTrailing.jsx"], "sourcesContent": ["import React from 'react';\r\n\r\nconst ProfitLoss13MonthDashboard = ({\r\n  headerTextStyle = {},\r\n  headingTextStyle = {},\r\n  subHeadingTextStyle = {},\r\n  subHeaderTextStyle = {},\r\n  contentTextStyle = {},\r\n  reportData = null // Add reportData prop to receive API data\r\n}) => {\r\n  // Extract background color from headerTextStyle for table header\r\n  const headerBgColor = headerTextStyle.color || '#20b2aa';\r\n\r\n  // Function to transform API data into the required format\r\n  const transformApiData = (apiData) => {\r\n    if (!apiData || !apiData.profitAndLossMonthsTrailing) {\r\n      return { months: [], tableData: [] };\r\n    }\r\n\r\n    const rawData = apiData.profitAndLossMonthsTrailing;\r\n    \r\n    // Extract months from the first data item (excluding account_type and account_name)\r\n    const months = rawData.length > 0 \r\n      ? Object.keys(rawData[0]).filter(key => key !== 'account_type' && key !== 'account_name')\r\n      : [];\r\n\r\n    // Group data by account_type\r\n    const groupedData = rawData.reduce((acc, item) => {\r\n      const accountType = item.account_type;\r\n      if (!acc[accountType]) {\r\n        acc[accountType] = [];\r\n      }\r\n      acc[accountType].push(item);\r\n      return acc;\r\n    }, {});\r\n\r\n    // Transform grouped data into table format\r\n    const tableData = [];\r\n    \r\n    Object.keys(groupedData).forEach(accountType => {\r\n      // Add header row for account type\r\n      tableData.push({\r\n        isHeader: true,\r\n        category: accountType\r\n      });\r\n\r\n      // Add data rows for this account type\r\n      groupedData[accountType].forEach(item => {\r\n        const rowData = months.map(month => item[month] || '0');\r\n        \r\n        // Check if this is a total row (contains \"Total\" in account_name)\r\n        const isTotal = item.account_name.toLowerCase().includes('total');\r\n        \r\n        tableData.push({\r\n          label: item.account_name,\r\n          data: rowData,\r\n          isTotal: isTotal,\r\n          indented: !isTotal && !item.account_name.toLowerCase().includes('total')\r\n        });\r\n      });\r\n    });\r\n\r\n    return { months, tableData };\r\n  };\r\n\r\n  // Transform the API data\r\n  const { months, tableData } = transformApiData(reportData);\r\n\r\n  // Fallback to empty state if no data\r\n  if (!reportData || !reportData.profitAndLossMonthsTrailing || months.length === 0) {\r\n    return (\r\n      <div className=\"p-5 section profit-loss-section month-trailing\">\r\n        <div className=\"max-w-8xl mx-auto bg-white p-10 mx-auto overflow-x-auto   report-container\">\r\n          <div className=\"report-header\">\r\n            <h4 style={{ ...subHeadingTextStyle, fontWeight: 'lighter', color: \"black\" }}>\r\n              Profit and Loss\r\n            </h4>\r\n            <div style={{ ...subHeadingTextStyle, fontWeight: 'lighter', color: \"black\" }}>\r\n              13 Month Trailing\r\n            </div>\r\n            <div style={{ ...contentTextStyle, fontSize: \"20px\" }}>\r\n              Acme Print\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"flex items-center justify-center h-64\">\r\n            <div className=\"text-center\">\r\n              <div className=\"text-lg text-gray-600\">No data available</div>\r\n              <div className=\"text-sm text-gray-500 mt-2\">Please check your data source</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const renderTableRow = (item, index) => {\r\n    if (item.isHeader) {\r\n      return (\r\n        <tr key={index} className=\"font-bold text-gray-800\">\r\n          <td className=\"text-left pl-2 font-normal\"  style={{ ...contentTextStyle, fontSize: \"15px\", fontWeight : 'light' }}>\r\n            <strong>{item.category}</strong>\r\n          </td>\r\n          {months.map((_, i) => (\r\n            <td \r\n              key={i}\r\n              style={{\r\n                backgroundColor: i === months.length - 1 ? '#d2e9ea' : 'transparent'\r\n              }}\r\n            ></td>\r\n          ))}\r\n        </tr>\r\n      );\r\n    }\r\n\r\n    if (item.isTotal) {\r\n      return (\r\n        <tr key={index} className=\"border-t-2 border-gray\">\r\n          <td className=\"text-left pl-2 font-normal\" style={{ ...contentTextStyle, fontSize: \"15px\", fontWeight : 'light' }}>\r\n            <strong>{item.label}</strong>\r\n          </td>\r\n          {item.data.map((value, i) => (\r\n            <td \r\n              key={i} \r\n              className=\"text-right font-mono\" \r\n              style={{\r\n                ...contentTextStyle,\r\n                backgroundColor: i === item.data.length - 1 ? '#d2e9ea' : 'transparent'\r\n              }}\r\n            >\r\n              <strong>{value}</strong>\r\n            </td>\r\n          ))}\r\n        </tr>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <tr key={index}>\r\n        <td\r\n          className={`text-left font-normal ${item.indented ? 'pl-6' : 'pl-2'}`}\r\n          style={{ ...contentTextStyle, fontSize: \"15px\", fontWeight : 'light' }}\r\n        >\r\n          {item.label}\r\n        </td>\r\n        {item.data.map((value, i) => (\r\n          <td \r\n            key={i} \r\n            className=\"text-right font-mono\" \r\n            style={{\r\n              ...contentTextStyle,\r\n              backgroundColor: i === item.data.length - 1 ? '#d2e9ea' : 'transparent'\r\n            }}\r\n          >\r\n            {value}\r\n          </td>\r\n        ))}\r\n      </tr>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className=\"p-5 section profit-loss-section\">\r\n      {/* Main Container */}\r\n      <div className=\"max-w-8xl mx-auto bg-white p-10 mx-auto overflow-x-auto  report-container\">\r\n        {/* Header Section */}\r\n        <div className=\"report-header\">\r\n          <h4\r\n            style={{ ...subHeadingTextStyle, fontWeight: 'lighter', color: \"black\" }}\r\n          >\r\n            Profit and Loss\r\n          </h4>\r\n          <div\r\n            style={{ ...subHeadingTextStyle, fontWeight: 'lighter', color: \"black\" }}\r\n          >\r\n            13 Month Trailing\r\n          </div>\r\n          <div\r\n            style={{ ...contentTextStyle, fontSize: \"20px\" }}\r\n          >\r\n            Acme Print\r\n          </div>\r\n        </div>\r\n\r\n        {/* Table */}\r\n        <div className=\"overflow-x-auto\">\r\n          <table className=\"w-full border-collapse text-sm mt-4 profit-loss-table\">\r\n            <thead className=\"table-header-group\">\r\n              <tr className=\"header-row\">\r\n                <th\r\n                  className=\"month-header\"\r\n                  style={{\r\n                    width: '200px',\r\n                    backgroundColor: headerBgColor,\r\n                    ...contentTextStyle,\r\n                    color: 'white'\r\n                  }}\r\n                >\r\n                 \r\n                </th>\r\n                {months.map((month, index) => (\r\n                  <th\r\n                    key={index}\r\n                    className=\"text-right text-white p-2 font-bold text-sm month-header\"\r\n                    style={{\r\n                      backgroundColor: headerBgColor,\r\n                      ...contentTextStyle,\r\n                      color: 'white',\r\n                      fontSize : '15px'\r\n                    }}\r\n                  >\r\n                    {month}\r\n                  </th>\r\n                ))}\r\n              </tr>\r\n            </thead>\r\n            <tbody className=\"table-row-group\">\r\n              {/* Render all transformed data */}\r\n              {tableData.map((item, index) => renderTableRow(item, `data-${index}`))}\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n        \r\n        <div className='text-center text-slate-300 text-xs border-b-4 border-blue-900 py-9'>\r\n          <p>\r\n            The information contained in this report is provided for informational purposes only and is not intended to substitute for obtaining accounting, tax, or financial advice from a professional accountant. Any tax advice\r\n            contained in this report is not intended to be used for the purpose of avoiding penalties under tax law. While we use reasonable efforts to furnish accurate and up-to-date information, we do not warrant that any\r\n            information contained in or made available through this report is accurate, complete, reliable, current or error-free. We assume no liability or responsibility for any errors or omissions in the content of this report or\r\n            delivered information.\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProfitLoss13MonthDashboard;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,0BAA0B,GAAGA,CAAC;EAClCC,eAAe,GAAG,CAAC,CAAC;EACpBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,mBAAmB,GAAG,CAAC,CAAC;EACxBC,kBAAkB,GAAG,CAAC,CAAC;EACvBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,UAAU,GAAG,IAAI,CAAC;AACpB,CAAC,KAAK;EACJ;EACA,MAAMC,aAAa,GAAGN,eAAe,CAACO,KAAK,IAAI,SAAS;;EAExD;EACA,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;IACpC,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACC,2BAA2B,EAAE;MACpD,OAAO;QAAEC,MAAM,EAAE,EAAE;QAAEC,SAAS,EAAE;MAAG,CAAC;IACtC;IAEA,MAAMC,OAAO,GAAGJ,OAAO,CAACC,2BAA2B;;IAEnD;IACA,MAAMC,MAAM,GAAGE,OAAO,CAACC,MAAM,GAAG,CAAC,GAC7BC,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC,CAAC,CAAC,CAAC,CAACI,MAAM,CAACC,GAAG,IAAIA,GAAG,KAAK,cAAc,IAAIA,GAAG,KAAK,cAAc,CAAC,GACvF,EAAE;;IAEN;IACA,MAAMC,WAAW,GAAGN,OAAO,CAACO,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;MAChD,MAAMC,WAAW,GAAGD,IAAI,CAACE,YAAY;MACrC,IAAI,CAACH,GAAG,CAACE,WAAW,CAAC,EAAE;QACrBF,GAAG,CAACE,WAAW,CAAC,GAAG,EAAE;MACvB;MACAF,GAAG,CAACE,WAAW,CAAC,CAACE,IAAI,CAACH,IAAI,CAAC;MAC3B,OAAOD,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEN;IACA,MAAMT,SAAS,GAAG,EAAE;IAEpBG,MAAM,CAACC,IAAI,CAACG,WAAW,CAAC,CAACO,OAAO,CAACH,WAAW,IAAI;MAC9C;MACAX,SAAS,CAACa,IAAI,CAAC;QACbE,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAEL;MACZ,CAAC,CAAC;;MAEF;MACAJ,WAAW,CAACI,WAAW,CAAC,CAACG,OAAO,CAACJ,IAAI,IAAI;QACvC,MAAMO,OAAO,GAAGlB,MAAM,CAACmB,GAAG,CAACC,KAAK,IAAIT,IAAI,CAACS,KAAK,CAAC,IAAI,GAAG,CAAC;;QAEvD;QACA,MAAMC,OAAO,GAAGV,IAAI,CAACW,YAAY,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC;QAEjEvB,SAAS,CAACa,IAAI,CAAC;UACbW,KAAK,EAAEd,IAAI,CAACW,YAAY;UACxBI,IAAI,EAAER,OAAO;UACbG,OAAO,EAAEA,OAAO;UAChBM,QAAQ,EAAE,CAACN,OAAO,IAAI,CAACV,IAAI,CAACW,YAAY,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO;QACzE,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO;MAAExB,MAAM;MAAEC;IAAU,CAAC;EAC9B,CAAC;;EAED;EACA,MAAM;IAAED,MAAM;IAAEC;EAAU,CAAC,GAAGJ,gBAAgB,CAACH,UAAU,CAAC;;EAE1D;EACA,IAAI,CAACA,UAAU,IAAI,CAACA,UAAU,CAACK,2BAA2B,IAAIC,MAAM,CAACG,MAAM,KAAK,CAAC,EAAE;IACjF,oBACEhB,OAAA;MAAKyC,SAAS,EAAC,gDAAgD;MAAAC,QAAA,eAC7D1C,OAAA;QAAKyC,SAAS,EAAC,4EAA4E;QAAAC,QAAA,gBACzF1C,OAAA;UAAKyC,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B1C,OAAA;YAAI2C,KAAK,EAAE;cAAE,GAAGvC,mBAAmB;cAAEwC,UAAU,EAAE,SAAS;cAAEnC,KAAK,EAAE;YAAQ,CAAE;YAAAiC,QAAA,EAAC;UAE9E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLhD,OAAA;YAAK2C,KAAK,EAAE;cAAE,GAAGvC,mBAAmB;cAAEwC,UAAU,EAAE,SAAS;cAAEnC,KAAK,EAAE;YAAQ,CAAE;YAAAiC,QAAA,EAAC;UAE/E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNhD,OAAA;YAAK2C,KAAK,EAAE;cAAE,GAAGrC,gBAAgB;cAAE2C,QAAQ,EAAE;YAAO,CAAE;YAAAP,QAAA,EAAC;UAEvD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhD,OAAA;UAAKyC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,eACpD1C,OAAA;YAAKyC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B1C,OAAA;cAAKyC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9DhD,OAAA;cAAKyC,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAA6B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5E;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAME,cAAc,GAAGA,CAAC1B,IAAI,EAAE2B,KAAK,KAAK;IACtC,IAAI3B,IAAI,CAACK,QAAQ,EAAE;MACjB,oBACE7B,OAAA;QAAgByC,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACjD1C,OAAA;UAAIyC,SAAS,EAAC,4BAA4B;UAAEE,KAAK,EAAE;YAAE,GAAGrC,gBAAgB;YAAE2C,QAAQ,EAAE,MAAM;YAAEL,UAAU,EAAG;UAAQ,CAAE;UAAAF,QAAA,eACjH1C,OAAA;YAAA0C,QAAA,EAASlB,IAAI,CAACM;UAAQ;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,EACJnC,MAAM,CAACmB,GAAG,CAAC,CAACoB,CAAC,EAAEC,CAAC,kBACfrD,OAAA;UAEE2C,KAAK,EAAE;YACLW,eAAe,EAAED,CAAC,KAAKxC,MAAM,CAACG,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG;UACzD;QAAE,GAHGqC,CAAC;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIH,CACN,CAAC;MAAA,GAXKG,KAAK;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAYV,CAAC;IAET;IAEA,IAAIxB,IAAI,CAACU,OAAO,EAAE;MAChB,oBACElC,OAAA;QAAgByC,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBAChD1C,OAAA;UAAIyC,SAAS,EAAC,4BAA4B;UAACE,KAAK,EAAE;YAAE,GAAGrC,gBAAgB;YAAE2C,QAAQ,EAAE,MAAM;YAAEL,UAAU,EAAG;UAAQ,CAAE;UAAAF,QAAA,eAChH1C,OAAA;YAAA0C,QAAA,EAASlB,IAAI,CAACc;UAAK;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,EACJxB,IAAI,CAACe,IAAI,CAACP,GAAG,CAAC,CAACuB,KAAK,EAAEF,CAAC,kBACtBrD,OAAA;UAEEyC,SAAS,EAAC,sBAAsB;UAChCE,KAAK,EAAE;YACL,GAAGrC,gBAAgB;YACnBgD,eAAe,EAAED,CAAC,KAAK7B,IAAI,CAACe,IAAI,CAACvB,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG;UAC5D,CAAE;UAAA0B,QAAA,eAEF1C,OAAA;YAAA0C,QAAA,EAASa;UAAK;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC,GAPnBK,CAAC;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQJ,CACL,CAAC;MAAA,GAfKG,KAAK;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgBV,CAAC;IAET;IAEA,oBACEhD,OAAA;MAAA0C,QAAA,gBACE1C,OAAA;QACEyC,SAAS,EAAE,yBAAyBjB,IAAI,CAACgB,QAAQ,GAAG,MAAM,GAAG,MAAM,EAAG;QACtEG,KAAK,EAAE;UAAE,GAAGrC,gBAAgB;UAAE2C,QAAQ,EAAE,MAAM;UAAEL,UAAU,EAAG;QAAQ,CAAE;QAAAF,QAAA,EAEtElB,IAAI,CAACc;MAAK;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,EACJxB,IAAI,CAACe,IAAI,CAACP,GAAG,CAAC,CAACuB,KAAK,EAAEF,CAAC,kBACtBrD,OAAA;QAEEyC,SAAS,EAAC,sBAAsB;QAChCE,KAAK,EAAE;UACL,GAAGrC,gBAAgB;UACnBgD,eAAe,EAAED,CAAC,KAAK7B,IAAI,CAACe,IAAI,CAACvB,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG;QAC5D,CAAE;QAAA0B,QAAA,EAEDa;MAAK,GAPDF,CAAC;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQJ,CACL,CAAC;IAAA,GAlBKG,KAAK;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAmBV,CAAC;EAET,CAAC;EAED,oBACEhD,OAAA;IAAKyC,SAAS,EAAC,iCAAiC;IAAAC,QAAA,eAE9C1C,OAAA;MAAKyC,SAAS,EAAC,2EAA2E;MAAAC,QAAA,gBAExF1C,OAAA;QAAKyC,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B1C,OAAA;UACE2C,KAAK,EAAE;YAAE,GAAGvC,mBAAmB;YAAEwC,UAAU,EAAE,SAAS;YAAEnC,KAAK,EAAE;UAAQ,CAAE;UAAAiC,QAAA,EAC1E;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLhD,OAAA;UACE2C,KAAK,EAAE;YAAE,GAAGvC,mBAAmB;YAAEwC,UAAU,EAAE,SAAS;YAAEnC,KAAK,EAAE;UAAQ,CAAE;UAAAiC,QAAA,EAC1E;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNhD,OAAA;UACE2C,KAAK,EAAE;YAAE,GAAGrC,gBAAgB;YAAE2C,QAAQ,EAAE;UAAO,CAAE;UAAAP,QAAA,EAClD;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhD,OAAA;QAAKyC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9B1C,OAAA;UAAOyC,SAAS,EAAC,uDAAuD;UAAAC,QAAA,gBACtE1C,OAAA;YAAOyC,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eACnC1C,OAAA;cAAIyC,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACxB1C,OAAA;gBACEyC,SAAS,EAAC,cAAc;gBACxBE,KAAK,EAAE;kBACLa,KAAK,EAAE,OAAO;kBACdF,eAAe,EAAE9C,aAAa;kBAC9B,GAAGF,gBAAgB;kBACnBG,KAAK,EAAE;gBACT;cAAE;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGA,CAAC,EACJnC,MAAM,CAACmB,GAAG,CAAC,CAACC,KAAK,EAAEkB,KAAK,kBACvBnD,OAAA;gBAEEyC,SAAS,EAAC,0DAA0D;gBACpEE,KAAK,EAAE;kBACLW,eAAe,EAAE9C,aAAa;kBAC9B,GAAGF,gBAAgB;kBACnBG,KAAK,EAAE,OAAO;kBACdwC,QAAQ,EAAG;gBACb,CAAE;gBAAAP,QAAA,EAEDT;cAAK,GATDkB,KAAK;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUR,CACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRhD,OAAA;YAAOyC,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAE/B5B,SAAS,CAACkB,GAAG,CAAC,CAACR,IAAI,EAAE2B,KAAK,KAAKD,cAAc,CAAC1B,IAAI,EAAE,QAAQ2B,KAAK,EAAE,CAAC;UAAC;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENhD,OAAA;QAAKyC,SAAS,EAAC,oEAAoE;QAAAC,QAAA,eACjF1C,OAAA;UAAA0C,QAAA,EAAG;QAKH;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACS,EAAA,GAxOIxD,0BAA0B;AA0OhC,eAAeA,0BAA0B;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}