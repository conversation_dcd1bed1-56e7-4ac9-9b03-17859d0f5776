import React from 'react';

const ProfitLossYTDDashboard = ({ 
  headerTextStyle = {},
  subHeadingTextStyle = {},
  contentTextStyle = {},
  reportData = null // Add reportData prop to receive API data
}) => {
  // Extract background color from headerTextStyle for table header
  const headerBgColor = headerTextStyle.color || '#20b2aa';

  // Function to transform API data into the required format
  const transformApiData = (apiData) => {
    if (!apiData || !apiData.profitAndLossMonthsYTD) {
      return { tableData: [], monthInfo: { current: 'Jan 25', previous: 'Jan 24' } };
    }

    const rawData = apiData.profitAndLossMonthsYTD;
    
    // Extract month information from the first item's keys
    let currentMonth = 'Jan 25';
    let previousMonth = 'Jan 24';
    
    if (rawData.length > 0) {
      const keys = Object.keys(rawData[0]);
      const actualsKey = keys.find(key => key.includes('_YTD_Actuals'));
      if (actualsKey) {
        const monthMatch = actualsKey.match(/([A-Za-z]+_\d+)_YTD_Actuals/);
        if (monthMatch) {
          currentMonth = monthMatch[1].replace('_', ' ');
          // Assume previous year is same month but year-1
          const [month, year] = currentMonth.split(' ');
          previousMonth = `${month} ${parseInt(year) - 1}`;
        }
      }
    }

    // Group data by account_type (or accountClassification if needed)
    const groupedData = rawData.reduce((acc, item) => {
      const accountType = item.account_type || item.accountClassification;
      if (!acc[accountType]) {
        acc[accountType] = [];
      }
      acc[accountType].push(item);
      return acc;
    }, {});

    // Transform grouped data into table format
    const tableData = [];
    
    Object.keys(groupedData).forEach(accountType => {
      // Add header row for account type
      tableData.push({
        isHeader: true,
        category: accountType
      });

      // Add data rows for this account type
      groupedData[accountType].forEach(item => {
        // Check if this is a total row (contains "Total" in account_name)
        const isTotal = item.account_name.toLowerCase().includes('total');
        
        // Determine if variance is positive or negative for styling
        const varianceAmount = parseFloat(item.Variance_Amount || '0');
        const variancePercent = parseFloat(item.Variance_Percentage || '0');
        const isPositive = varianceAmount > 0;
        const isNegative = varianceAmount < 0;
        
        tableData.push({
          label: item.account_name,
          jan25: item.Apr_25_YTD_Actuals || '0', // Using Apr as example, adjust field names as needed
          jan25Percent: item.Apr_25_Percent_of_Income || '0%',
          jan24: item.Apr_24_YTD_Prior_Year || '0',
          jan24Percent: item.Apr_24_Percent_of_Income || '0%',
          variance: item.Variance_Amount || '0',
          variancePercent: item.Variance_Percentage || '0%',
          isTotal: isTotal,
          indented: !isTotal && !item.account_name.toLowerCase().includes('total'),
          isPositive: isPositive,
          isNegative: isNegative
        });
      });
    });

    return { 
      tableData, 
      monthInfo: { 
        current: currentMonth, 
        previous: previousMonth 
      } 
    };
  };

  // Transform the API data
  const { tableData, monthInfo } = transformApiData(reportData);

  // Fallback to empty state if no data
  if (!reportData || !reportData.profitAndLossMonthsYTD || tableData.length === 0) {
    return (
      <div className="min-h-screen p-5 section profit-loss-section">
        <div className="max-w-6xl mx-auto bg-white p-10 mx-auto overflow-x-auto report-container">
          
          
          <div>
            <h2 style={{ ...subHeadingTextStyle, fontWeight: 'lighter', color: "black" }}>
              Profit and Loss
            </h2>
            <div style={{ ...contentTextStyle, fontSize: "20px" }}>
              Year to Date Report
            </div>
            <p style={{ ...contentTextStyle, fontSize: "20px", fontWeight: "100" }}>
              Acme Print
            </p>
          </div>

          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="text-lg text-gray-600">No data available</div>
              <div className="text-sm text-gray-500 mt-2">Please check your data source</div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const renderTableRow = (item, index) => {
    if (item.isHeader) {
      return (
        <tr key={index} className="font-bold text-gray-800">
          <td className="text-left pl-2 font-normal" style={{ ...contentTextStyle, fontSize: '15px' }}>
            <strong>{item.category}</strong>
          </td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
        </tr>
      );
    }

    if (item.isTotal) {
      return (
        <tr key={index} className="border-t-2 border-gray">
          <td className="text-left pl-2 font-normal" style={{ ...contentTextStyle, fontSize: '15px' }}>
            <strong>{item.label}</strong>
          </td>
          <td className="text-right font-mono" style={{ ...contentTextStyle, fontSize: '15px' }}>
            <strong>{item.jan25}</strong>
          </td>
          <td className="text-right" style={{ ...contentTextStyle, fontSize: '15px' }}>
            <strong>{item.jan25Percent}</strong>
          </td>
          <td className="text-right font-mono" style={{ ...contentTextStyle, fontSize: '15px' }}>
            <strong>{item.jan24}</strong>
          </td>
          <td className="text-right" style={{ ...contentTextStyle, fontSize: '15px' }}>
            <strong>{item.jan24Percent}</strong>
          </td>
          <td 
            className={`text-right font-mono ${item.isPositive ? 'text-green-600' : item.isNegative ? 'text-red-600' : ''}`}
            style={{ ...contentTextStyle, fontSize: '15px' }}
          >
            <strong>{item.variance}</strong>
          </td>
          <td 
            className={`text-right ${item.isPositive ? 'text-green-600' : item.isNegative ? 'text-red-600' : ''}`}
            style={{ ...contentTextStyle, fontSize: '15px' }}
          >
            <strong>{item.variancePercent}</strong>
          </td>
        </tr>
      );
    }

    return (
      <tr key={index}>
        <td 
          className={`text-left font-normal ${item.indented ? 'pl-6' : 'pl-2'}`}
          style={{ ...contentTextStyle, fontSize: '15px' }}
        >
          {item.label}
        </td>
        <td className="text-right font-mono" style={{ ...contentTextStyle, fontSize: '15px' }}>
          {item.jan25}
        </td>
        <td className="text-right" style={{ ...contentTextStyle, fontSize: '15px' }}>
          {item.jan25Percent}
        </td>
        <td className="text-right font-mono" style={{ ...contentTextStyle, fontSize: '15px' }}>
          {item.jan24}
        </td>
        <td className="text-right" style={{ ...contentTextStyle, fontSize: '15px' }}>
          {item.jan24Percent}
        </td>
        <td 
          className={`text-right font-mono ${item.isPositive ? 'text-green-600' : item.isNegative ? 'text-red-600' : ''}`}
          style={{ ...contentTextStyle, fontSize: '15px' }}
        >
          {item.variance}
        </td>
        <td 
          className={`text-right ${item.isPositive ? 'text-green-600' : item.isNegative ? 'text-red-600' : ''}`}
          style={{ ...contentTextStyle, fontSize: '15px' }}
        >
          {item.variancePercent}
        </td>
      </tr>
    );
  };

  // Generate dynamic YTD date description
  const getYTDDescription = () => {
    if (monthInfo.current) {
      const [month, year] = monthInfo.current.split(' ');
      const monthNames = {
        'Jan': 'January', 'Feb': 'February', 'Mar': 'March', 'Apr': 'April',
        'May': 'May', 'Jun': 'June', 'Jul': 'July', 'Aug': 'August',
        'Sep': 'September', 'Oct': 'October', 'Nov': 'November', 'Dec': 'December'
      };
      const fullMonth = monthNames[month] || month;
      return `Year to Date As of ${fullMonth} 31st, 20${year}`;
    }
    return 'Year to Date As of January 31st, 2025';
  };

  return (
    <div className="min-h-screen p-5 section profit-loss-section">
      {/* Main Container */}
      <div className="max-w-6xl mx-auto bg-white p-10 mx-auto overflow-x-auto report-container">
        {/* Header Section */}
        <div>
          <h2
            className="text-2xl font-light text-gray-800 m-0"
           style={{...subHeadingTextStyle,  fontWeight: 'lighter', color : "black"}}
          >
            Profit and Loss
          </h2>
          <div
            className="text-xl text-gray-800"
            style={{...contentTextStyle, fontSize : "20px"}}
          >
            {getYTDDescription()}
          </div>
          <p
            style={{...contentTextStyle, fontSize : "20px", fontWeight : "100"}}
          >
            Acme Print
          </p>
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          <table className="w-full border-collapse text-sm mt-4 profit-loss-table">
             <thead className="table-header-group">
              {/* Month Header Row */}
              <tr className="text-black text-center bg-white">
                <th
                  className="text-left bg-white border-0"
                  style={{ ...contentTextStyle, fontSize: '15px', color: 'black' }}
                ></th>
                <th
                  className="text-left bg-white border-0 pl-10"
                  style={{ ...contentTextStyle, fontSize: '15px', color: 'black' }}
                  colSpan="2"
                >
                  {monthInfo.current}
                </th>
                <th
                  className="text-left bg-white border-0 pl-10"
                  style={{ ...contentTextStyle, fontSize: '15px', color: 'black' }}
                  colSpan="2"
                >
                  {monthInfo.previous}
                </th>
                <th
                  className="text-left bg-white border-0"
                  style={{ ...contentTextStyle, fontSize: '15px', color: 'black' }}
                  colSpan="2"
                ></th>
              </tr>
              {/* Column Header Row */}
              <tr>
                <th
                  className="text-right text-white p-2 font-bold text-sm"
                  style={{
                    ...contentTextStyle,
                    fontSize: '15px',
                    color: 'white'
                  }}
                ></th>
                {/* Current Year Group */}
                <th
                  className="text-center text-white p-2 font-bold text-sm"
                  style={{
                    backgroundColor: headerBgColor,
                    ...contentTextStyle,
                    fontSize: '15px',
                    color: 'white'
                  }}
                >
                  Actuals
                </th>
                <th
                  className="text-center text-white p-2 font-bold text-sm"
                  style={{
                    backgroundColor: headerBgColor,
                    ...contentTextStyle,
                    fontSize: '15px',
                    color: 'white',
                    borderRight: '20px solid white'
                  }}
                >
                  <div>% of Income</div>
                </th>

                {/* Previous Year Group */}
                <th
                  className="text-center text-white p-1 font-bold text-sm"
                  style={{
                    backgroundColor: headerBgColor,
                    ...contentTextStyle,
                    fontSize: '15px',
                    color: 'white'
                  }}
                >
                  Prior Year
                </th>
                <th
                  className="text-center text-white p-1 font-bold text-sm"
                  style={{
                    backgroundColor: headerBgColor,
                    ...contentTextStyle,
                    fontSize: '15px',
                    color: 'white',
                    borderRight: '20px solid white'
                  }}
                >
                  <div>% of Income</div>
                </th>

                {/* Variance Group */}
                <th
                  className="text-center text-white p-2 font-bold text-sm"
                  style={{
                    backgroundColor: headerBgColor,
                    ...contentTextStyle,
                    fontSize: '15px',
                    color: 'white'
                  }}
                >
                  $ Variance
                </th>
                <th
                  className="text-center text-white p-2 font-bold text-sm"
                  style={{
                    backgroundColor: headerBgColor,
                    ...contentTextStyle,
                    fontSize: '15px',
                    color: 'white'
                  }}
                >
                  % Variance
                </th>
              </tr>
            </thead>
            <tbody className="table-row-group">
              {tableData.map((item, index) => renderTableRow(item, index))}
            </tbody>
          </table>
        </div>
        <div className='text-center text-slate-300 text-xs border-b-4 border-blue-900 py-9'>
          <p>
            The information contained in this report is provided for informational purposes only and is not intended to substitute for obtaining accounting, tax, or financial advice from a professional accountant. Any tax advice
            contained in this report is not intended to be used for the purpose of avoiding penalties under tax law. While we use reasonable efforts to furnish accurate and up-to-date information, we do not warrant that any
            information contained in or made available through this report is accurate, complete, reliable, current or error-free. We assume no liability or responsibility for any errors or omissions in the content of this report or
            delivered information.
          </p>
        </div>
      </div>
    </div>
  );
};

export default ProfitLossYTDDashboard;