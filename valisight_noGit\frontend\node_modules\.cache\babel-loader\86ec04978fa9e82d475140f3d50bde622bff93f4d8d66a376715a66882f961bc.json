{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JASON_NEW\\\\valisight_noGit\\\\frontend\\\\src\\\\pages\\\\reports\\\\ReportPages\\\\Monthly.jsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfitLossMonthlyDashboard = ({\n  headerTextStyle = {},\n  subHeadingTextStyle = {},\n  contentTextStyle = {},\n  reportData = null // Add reportData prop to receive API data\n}) => {\n  // Extract background color from headerTextStyle for table header\n  const headerBgColor = headerTextStyle.color || '#20b2aa';\n\n  // Function to transform API data into the required format\n  const transformApiData = apiData => {\n    if (!apiData || !apiData.profitAndLossMonthly) {\n      return {\n        tableData: [],\n        monthInfo: {\n          current: 'Jan 25',\n          previous: 'Jan 24'\n        }\n      };\n    }\n    const rawData = apiData.profitAndLossMonthly;\n\n    // Extract month information from the first item's keys\n    let currentMonth = 'Jan 25';\n    let previousMonth = 'Jan 24';\n    if (rawData.length > 0) {\n      const keys = Object.keys(rawData[0]);\n      const actualsKey = keys.find(key => key.includes('_Actuals'));\n      if (actualsKey) {\n        const monthMatch = actualsKey.match(/([A-Za-z]+_\\d+)_Actuals/);\n        if (monthMatch) {\n          currentMonth = monthMatch[1].replace('_', ' ');\n          // Assume previous year is same month but year-1\n          const [month, year] = currentMonth.split(' ');\n          previousMonth = `${month} ${parseInt(year) - 1}`;\n        }\n      }\n    }\n\n    // Group data by account_type\n    const groupedData = rawData.reduce((acc, item) => {\n      const accountType = item.account_type;\n      if (!acc[accountType]) {\n        acc[accountType] = [];\n      }\n      acc[accountType].push(item);\n      return acc;\n    }, {});\n\n    // Transform grouped data into table format\n    const tableData = [];\n    Object.keys(groupedData).forEach(accountType => {\n      // Add header row for account type\n      tableData.push({\n        isHeader: true,\n        category: accountType\n      });\n\n      // Add data rows for this account type\n      groupedData[accountType].forEach(item => {\n        // Check if this is a total row (contains \"Total\" in account_name)\n        const isTotal = item.account_name.toLowerCase().includes('total');\n\n        // Determine if variance is positive or negative for styling\n        const varianceAmount = parseFloat(item.Variance_Amount || '0');\n        const variancePercent = parseFloat(item.Variance_Percentage || '0');\n        const isPositive = varianceAmount > 0;\n        const isNegative = varianceAmount < 0;\n        tableData.push({\n          label: item.account_name,\n          jan25: item.Apr_25_Actuals || '0',\n          // Using Apr as example, adjust field names as needed\n          jan25Percent: item.Apr_25_Percent_of_Income || '0%',\n          jan24: item.Apr_24_Prior_Year || '0',\n          jan24Percent: item.Apr_24_Percent_of_Income || '0%',\n          variance: item.Variance_Amount || '0',\n          variancePercent: item.Variance_Percentage || '0%',\n          isTotal: isTotal,\n          indented: !isTotal && !item.account_name.toLowerCase().includes('total'),\n          isPositive: isPositive,\n          isNegative: isNegative\n        });\n      });\n    });\n    return {\n      tableData,\n      monthInfo: {\n        current: currentMonth,\n        previous: previousMonth\n      }\n    };\n  };\n\n  // Transform the API data\n  const {\n    tableData,\n    monthInfo\n  } = transformApiData(reportData);\n\n  // Fallback to empty state if no data\n  if (!reportData || !reportData.profitAndLossMonthly || tableData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen p-5 section profit-loss-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto bg-white p-10 mx-auto overflow-x-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"   \", /*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: 'lighter',\n              color: \"black\"\n            },\n            children: \"Profit and Loss\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: 'lighter',\n              color: \"black\"\n            },\n            children: \"Monthly Report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              ...contentTextStyle,\n              fontSize: \"20px\"\n            },\n            children: \"Acme Print\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center h-64\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg text-gray-600\",\n              children: \"No data available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-500 mt-2\",\n              children: \"Please check your data source\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this);\n  }\n  const renderTableRow = (item, index) => {\n    if (item.isHeader) {\n      return /*#__PURE__*/_jsxDEV(\"tr\", {\n        className: \"font-bold text-gray-800\",\n        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-left pl-2 font-normal\",\n          style: {\n            ...contentTextStyle,\n            fontSize: '15px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: item.category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this);\n    }\n    if (item.isTotal) {\n      return /*#__PURE__*/_jsxDEV(\"tr\", {\n        className: \"border-t-2 border-gray\",\n        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-left pl-2 font-normal\",\n          style: {\n            ...contentTextStyle,\n            fontSize: '15px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right font-mono\",\n          style: {\n            ...contentTextStyle,\n            fontSize: '15px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: item.jan25\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right\",\n          style: {\n            ...contentTextStyle,\n            fontSize: '15px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: item.jan25Percent\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right font-mono\",\n          style: {\n            ...contentTextStyle,\n            fontSize: '15px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: item.jan24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right\",\n          style: {\n            ...contentTextStyle,\n            fontSize: '15px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: item.jan24Percent\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: `text-right font-mono ${item.isPositive ? 'text-green-600' : item.isNegative ? 'text-red-600' : ''}`,\n          style: {\n            ...contentTextStyle,\n            fontSize: '15px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: item.variance\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: `text-right ${item.isPositive ? 'text-green-600' : item.isNegative ? 'text-red-600' : ''}`,\n          style: {\n            ...contentTextStyle,\n            fontSize: '15px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: item.variancePercent\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"tr\", {\n      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n        className: `text-left font-normal ${item.indented ? 'pl-6' : 'pl-2'}`,\n        style: {\n          ...contentTextStyle,\n          fontSize: '15px'\n        },\n        children: item.label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"text-right font-mono\",\n        style: {\n          ...contentTextStyle,\n          fontSize: '15px'\n        },\n        children: item.jan25\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"text-right\",\n        style: {\n          ...contentTextStyle,\n          fontSize: '15px'\n        },\n        children: item.jan25Percent\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"text-right font-mono\",\n        style: {\n          ...contentTextStyle,\n          fontSize: '15px'\n        },\n        children: item.jan24\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"text-right\",\n        style: {\n          ...contentTextStyle,\n          fontSize: '15px'\n        },\n        children: item.jan24Percent\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: `text-right font-mono ${item.isPositive ? 'text-green-600' : item.isNegative ? 'text-red-600' : ''}`,\n        style: {\n          ...contentTextStyle,\n          fontSize: '15px'\n        },\n        children: item.variance\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: `text-right ${item.isPositive ? 'text-green-600' : item.isNegative ? 'text-red-600' : ''}`,\n        style: {\n          ...contentTextStyle,\n          fontSize: '15px'\n        },\n        children: item.variancePercent\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this)]\n    }, index, true, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen p-5 section profit-loss-section\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl mx-auto bg-white p-10 mx-auto overflow-x-auto report-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          style: {\n            ...subHeadingTextStyle,\n            fontWeight: 'lighter',\n            color: \"black\"\n          },\n          children: \"Profit and Loss\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...subHeadingTextStyle,\n            fontWeight: 'lighter',\n            color: \"black\"\n          },\n          children: monthInfo.current\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...contentTextStyle,\n            fontSize: \"20px\"\n          },\n          children: \"Acme Print\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"w-full border-collapse text-sm mt-4 profit-loss-table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"table-header-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"text-black text-center bg-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-left bg-white border-0\",\n                style: {\n                  ...contentTextStyle,\n                  fontSize: '15px',\n                  color: 'black'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-left bg-white border-0 pl-10\",\n                style: {\n                  ...contentTextStyle,\n                  fontSize: '15px',\n                  color: 'black'\n                },\n                colSpan: \"2\",\n                children: monthInfo.current\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-left bg-white border-0 pl-10\",\n                style: {\n                  ...contentTextStyle,\n                  fontSize: '15px',\n                  color: 'black'\n                },\n                colSpan: \"2\",\n                children: monthInfo.previous\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-left bg-white border-0\",\n                style: {\n                  ...contentTextStyle,\n                  fontSize: '15px',\n                  color: 'black'\n                },\n                colSpan: \"2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-right text-white p-2 font-bold text-sm\",\n                style: {\n                  ...contentTextStyle,\n                  fontSize: '15px',\n                  color: 'white'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-center text-white p-2 font-bold text-sm\",\n                style: {\n                  backgroundColor: headerBgColor,\n                  ...contentTextStyle,\n                  fontSize: '15px',\n                  color: 'white'\n                },\n                children: \"Actuals\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-center text-white p-2 font-bold text-sm\",\n                style: {\n                  backgroundColor: headerBgColor,\n                  ...contentTextStyle,\n                  fontSize: '15px',\n                  color: 'white',\n                  borderRight: '20px solid white'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [\"% of\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 297,\n                    columnNumber: 28\n                  }, this), \"Income\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-center text-white p-1 font-bold text-sm\",\n                style: {\n                  backgroundColor: headerBgColor,\n                  ...contentTextStyle,\n                  fontSize: '15px',\n                  color: 'white'\n                },\n                children: \"Prior Year\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-center text-white p-1 font-bold text-sm\",\n                style: {\n                  backgroundColor: headerBgColor,\n                  ...contentTextStyle,\n                  fontSize: '15px',\n                  color: 'white',\n                  borderRight: '20px solid white'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [\"% of\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 322,\n                    columnNumber: 28\n                  }, this), \"Income\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-center text-white p-2 font-bold text-sm\",\n                style: {\n                  backgroundColor: headerBgColor,\n                  ...contentTextStyle,\n                  fontSize: '15px',\n                  color: 'white'\n                },\n                children: \"$ Variance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-center text-white p-2 font-bold text-sm\",\n                style: {\n                  backgroundColor: headerBgColor,\n                  ...contentTextStyle,\n                  fontSize: '15px',\n                  color: 'white'\n                },\n                children: \"% Variance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"table-row-group\",\n            children: tableData.map((item, index) => renderTableRow(item, index))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center text-slate-300 text-xs border-b-4 border-blue-900 py-9\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"The information contained in this report is provided for informational purposes only and is not intended to substitute for obtaining accounting, tax, or financial advice from a professional accountant. Any tax advice contained in this report is not intended to be used for the purpose of avoiding penalties under tax law. While we use reasonable efforts to furnish accurate and up-to-date information, we do not warrant that any information contained in or made available through this report is accurate, complete, reliable, current or error-free. We assume no liability or responsibility for any errors or omissions in the content of this report or delivered information.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 355,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 213,\n    columnNumber: 5\n  }, this);\n};\n_c = ProfitLossMonthlyDashboard;\nexport default ProfitLossMonthlyDashboard;\nvar _c;\n$RefreshReg$(_c, \"ProfitLossMonthlyDashboard\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ProfitLossMonthlyDashboard", "headerTextStyle", "subHeadingTextStyle", "contentTextStyle", "reportData", "headerBgColor", "color", "transformApiData", "apiData", "profitAndLossMonthly", "tableData", "monthInfo", "current", "previous", "rawData", "currentMonth", "previousMonth", "length", "keys", "Object", "actualsKey", "find", "key", "includes", "monthMatch", "match", "replace", "month", "year", "split", "parseInt", "groupedData", "reduce", "acc", "item", "accountType", "account_type", "push", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>", "category", "isTotal", "account_name", "toLowerCase", "varianceAmount", "parseFloat", "Variance_Amount", "variancePercent", "Variance_Percentage", "isPositive", "isNegative", "label", "jan25", "Apr_25_Actuals", "jan25<PERSON><PERSON><PERSON>", "Apr_25_Percent_of_Income", "jan24", "Apr_24_Prior_Year", "jan24<PERSON>ercent", "Apr_24_Percent_of_Income", "variance", "indented", "className", "children", "style", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "renderTableRow", "index", "colSpan", "backgroundColor", "borderRight", "map", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/JASON_NEW/valisight_noGit/frontend/src/pages/reports/ReportPages/Monthly.jsx"], "sourcesContent": ["import React from 'react';\r\n\r\nconst ProfitLossMonthlyDashboard = ({\r\n  headerTextStyle = {},\r\n  subHeadingTextStyle = {},\r\n  contentTextStyle = {},\r\n  reportData = null // Add reportData prop to receive API data\r\n}) => {\r\n  // Extract background color from headerTextStyle for table header\r\n  const headerBgColor = headerTextStyle.color || '#20b2aa';\r\n\r\n  // Function to transform API data into the required format\r\n  const transformApiData = (apiData) => {\r\n    if (!apiData || !apiData.profitAndLossMonthly) {\r\n      return { tableData: [], monthInfo: { current: 'Jan 25', previous: 'Jan 24' } };\r\n    }\r\n\r\n    const rawData = apiData.profitAndLossMonthly;\r\n    \r\n    // Extract month information from the first item's keys\r\n    let currentMonth = 'Jan 25';\r\n    let previousMonth = 'Jan 24';\r\n    \r\n    if (rawData.length > 0) {\r\n      const keys = Object.keys(rawData[0]);\r\n      const actualsKey = keys.find(key => key.includes('_Actuals'));\r\n      if (actualsKey) {\r\n        const monthMatch = actualsKey.match(/([A-Za-z]+_\\d+)_Actuals/);\r\n        if (monthMatch) {\r\n          currentMonth = monthMatch[1].replace('_', ' ');\r\n          // Assume previous year is same month but year-1\r\n          const [month, year] = currentMonth.split(' ');\r\n          previousMonth = `${month} ${parseInt(year) - 1}`;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Group data by account_type\r\n    const groupedData = rawData.reduce((acc, item) => {\r\n      const accountType = item.account_type;\r\n      if (!acc[accountType]) {\r\n        acc[accountType] = [];\r\n      }\r\n      acc[accountType].push(item);\r\n      return acc;\r\n    }, {});\r\n\r\n    // Transform grouped data into table format\r\n    const tableData = [];\r\n    \r\n    Object.keys(groupedData).forEach(accountType => {\r\n      // Add header row for account type\r\n      tableData.push({\r\n        isHeader: true,\r\n        category: accountType\r\n      });\r\n\r\n      // Add data rows for this account type\r\n      groupedData[accountType].forEach(item => {\r\n        // Check if this is a total row (contains \"Total\" in account_name)\r\n        const isTotal = item.account_name.toLowerCase().includes('total');\r\n        \r\n        // Determine if variance is positive or negative for styling\r\n        const varianceAmount = parseFloat(item.Variance_Amount || '0');\r\n        const variancePercent = parseFloat(item.Variance_Percentage || '0');\r\n        const isPositive = varianceAmount > 0;\r\n        const isNegative = varianceAmount < 0;\r\n        \r\n        tableData.push({\r\n          label: item.account_name,\r\n          jan25: item.Apr_25_Actuals || '0', // Using Apr as example, adjust field names as needed\r\n          jan25Percent: item.Apr_25_Percent_of_Income || '0%',\r\n          jan24: item.Apr_24_Prior_Year || '0',\r\n          jan24Percent: item.Apr_24_Percent_of_Income || '0%',\r\n          variance: item.Variance_Amount || '0',\r\n          variancePercent: item.Variance_Percentage || '0%',\r\n          isTotal: isTotal,\r\n          indented: !isTotal && !item.account_name.toLowerCase().includes('total'),\r\n          isPositive: isPositive,\r\n          isNegative: isNegative\r\n        });\r\n      });\r\n    });\r\n\r\n    return { \r\n      tableData, \r\n      monthInfo: { \r\n        current: currentMonth, \r\n        previous: previousMonth \r\n      } \r\n    };\r\n  };\r\n\r\n  // Transform the API data\r\n  const { tableData, monthInfo } = transformApiData(reportData);\r\n\r\n  // Fallback to empty state if no data\r\n  if (!reportData || !reportData.profitAndLossMonthly || tableData.length === 0) {\r\n    return (\r\n      <div className=\"min-h-screen p-5 section profit-loss-section\">\r\n        <div className=\"max-w-6xl mx-auto bg-white p-10 mx-auto overflow-x-auto\">      \r\n          <div>   {/* className=\"report-header\" */}\r\n            <h2 style={{ ...subHeadingTextStyle, fontWeight: 'lighter', color: \"black\" }}>\r\n              Profit and Loss\r\n            </h2>\r\n            <div style={{ ...subHeadingTextStyle, fontWeight: 'lighter', color: \"black\" }}>\r\n              Monthly Report\r\n            </div>\r\n            <div style={{ ...contentTextStyle, fontSize: \"20px\" }}>\r\n              Acme Print\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"flex items-center justify-center h-64\">\r\n            <div className=\"text-center\">\r\n              <div className=\"text-lg text-gray-600\">No data available</div>\r\n              <div className=\"text-sm text-gray-500 mt-2\">Please check your data source</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const renderTableRow = (item, index) => {\r\n    if (item.isHeader) {\r\n      return (\r\n        <tr key={index} className=\"font-bold text-gray-800\">\r\n          <td className=\"text-left pl-2 font-normal\" style={{ ...contentTextStyle, fontSize: '15px' }}>\r\n            <strong>{item.category}</strong>\r\n          </td>\r\n          <td></td>\r\n          <td></td>\r\n          <td></td>\r\n          <td></td>\r\n          <td></td>\r\n          <td></td>\r\n        </tr>\r\n      );\r\n    }\r\n\r\n    if (item.isTotal) {\r\n      return (\r\n        <tr key={index} className=\"border-t-2 border-gray\">\r\n          <td className=\"text-left pl-2 font-normal\" style={{ ...contentTextStyle, fontSize: '15px' }}>\r\n            <strong>{item.label}</strong>\r\n          </td>\r\n          <td className=\"text-right font-mono\" style={{ ...contentTextStyle, fontSize: '15px' }}>\r\n            <strong>{item.jan25}</strong>\r\n          </td>\r\n          <td className=\"text-right\" style={{ ...contentTextStyle, fontSize: '15px' }}>\r\n            <strong>{item.jan25Percent}</strong>\r\n          </td>\r\n          <td className=\"text-right font-mono\" style={{ ...contentTextStyle, fontSize: '15px' }}>\r\n            <strong>{item.jan24}</strong>\r\n          </td>\r\n          <td className=\"text-right\" style={{ ...contentTextStyle, fontSize: '15px' }}>\r\n            <strong>{item.jan24Percent}</strong>\r\n          </td>\r\n          <td\r\n            className={`text-right font-mono ${item.isPositive ? 'text-green-600' : item.isNegative ? 'text-red-600' : ''}`}\r\n            style={{ ...contentTextStyle, fontSize: '15px' }}\r\n          >\r\n            <strong>{item.variance}</strong>\r\n          </td>\r\n          <td\r\n            className={`text-right ${item.isPositive ? 'text-green-600' : item.isNegative ? 'text-red-600' : ''}`}\r\n            style={{ ...contentTextStyle, fontSize: '15px' }}\r\n          >\r\n            <strong>{item.variancePercent}</strong>\r\n          </td>\r\n        </tr>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <tr key={index}>\r\n        <td\r\n          className={`text-left font-normal ${item.indented ? 'pl-6' : 'pl-2'}`}\r\n          style={{ ...contentTextStyle, fontSize: '15px' }}\r\n        >\r\n          {item.label}\r\n        </td>\r\n        <td className=\"text-right font-mono\" style={{ ...contentTextStyle, fontSize: '15px' }}>\r\n          {item.jan25}\r\n        </td>\r\n        <td className=\"text-right\" style={{ ...contentTextStyle, fontSize: '15px' }}>\r\n          {item.jan25Percent}\r\n        </td>\r\n        <td className=\"text-right font-mono\" style={{ ...contentTextStyle, fontSize: '15px' }}>\r\n          {item.jan24}\r\n        </td>\r\n        <td className=\"text-right\" style={{ ...contentTextStyle, fontSize: '15px' }}>\r\n          {item.jan24Percent}\r\n        </td>\r\n        <td\r\n          className={`text-right font-mono ${item.isPositive ? 'text-green-600' : item.isNegative ? 'text-red-600' : ''}`}\r\n          style={{ ...contentTextStyle, fontSize: '15px' }}\r\n        >\r\n          {item.variance}\r\n        </td>\r\n        <td\r\n          className={`text-right ${item.isPositive ? 'text-green-600' : item.isNegative ? 'text-red-600' : ''}`}\r\n          style={{ ...contentTextStyle, fontSize: '15px' }}\r\n        >\r\n          {item.variancePercent}\r\n        </td>\r\n      </tr>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen p-5 section profit-loss-section\">\r\n      {/* Main Container */}\r\n      <div className=\"max-w-6xl mx-auto bg-white p-10 mx-auto overflow-x-auto report-container\">\r\n    {/* Header Section */}\r\n        <div>\r\n          <h2\r\n            style={{ ...subHeadingTextStyle, fontWeight: 'lighter', color: \"black\" }}\r\n          >\r\n            Profit and Loss\r\n          </h2>\r\n          <div\r\n            style={{ ...subHeadingTextStyle, fontWeight: 'lighter', color: \"black\" }}\r\n          >\r\n            {monthInfo.current}\r\n          </div>\r\n          <div\r\n            style={{ ...contentTextStyle, fontSize: \"20px\" }}\r\n          >\r\n            Acme Print\r\n          </div>\r\n        </div>\r\n\r\n        {/* Table */}\r\n        <div className=\"overflow-x-auto\">\r\n          <table className=\"w-full border-collapse text-sm mt-4 profit-loss-table\">\r\n            <thead className=\"table-header-group\">\r\n              {/* Month Header Row */}\r\n              <tr className=\"text-black text-center bg-white\">\r\n                <th\r\n                  className=\"text-left bg-white border-0\"\r\n                  style={{ ...contentTextStyle, fontSize: '15px', color: 'black' }}\r\n                ></th>\r\n                <th\r\n                  className=\"text-left bg-white border-0 pl-10\"\r\n                  style={{ ...contentTextStyle, fontSize: '15px', color: 'black' }}\r\n                  colSpan=\"2\"\r\n                >\r\n                  {monthInfo.current}\r\n                </th>\r\n                <th\r\n                  className=\"text-left bg-white border-0 pl-10\"\r\n                  style={{ ...contentTextStyle, fontSize: '15px', color: 'black' }}\r\n                  colSpan=\"2\"\r\n                >\r\n                  {monthInfo.previous}\r\n                </th>\r\n                <th\r\n                  className=\"text-left bg-white border-0\"\r\n                  style={{ ...contentTextStyle, fontSize: '15px', color: 'black' }}\r\n                  colSpan=\"2\"\r\n                ></th>\r\n              </tr>\r\n              {/* Column Header Row */}\r\n              <tr>\r\n                <th\r\n                  className=\"text-right text-white p-2 font-bold text-sm\"\r\n                  style={{\r\n                    ...contentTextStyle,\r\n                    fontSize: '15px',\r\n                    color: 'white'\r\n                  }}\r\n                ></th>\r\n                {/* Current Month Group */}\r\n                <th\r\n                  className=\"text-center text-white p-2 font-bold text-sm\"\r\n                  style={{\r\n                    backgroundColor: headerBgColor,\r\n                    ...contentTextStyle,\r\n                    fontSize: '15px',\r\n                    color: 'white'\r\n                  }}\r\n                >\r\n                  Actuals\r\n                </th>\r\n                <th\r\n                  className=\"text-center text-white p-2 font-bold text-sm\"\r\n                  style={{\r\n                    backgroundColor: headerBgColor,\r\n                    ...contentTextStyle,\r\n                    fontSize: '15px',\r\n                    color: 'white',\r\n                    borderRight: '20px solid white'\r\n                  }}\r\n                >\r\n                  <div>% of<br />Income</div>\r\n                </th>\r\n\r\n                {/* Previous Year Group */}\r\n                <th\r\n                  className=\"text-center text-white p-1 font-bold text-sm\"\r\n                  style={{\r\n                    backgroundColor: headerBgColor,\r\n                    ...contentTextStyle,\r\n                    fontSize: '15px',\r\n                    color: 'white'\r\n                  }}\r\n                >\r\n                  Prior Year\r\n                </th>\r\n                <th\r\n                  className=\"text-center text-white p-1 font-bold text-sm\"\r\n                  style={{\r\n                    backgroundColor: headerBgColor,\r\n                    ...contentTextStyle,\r\n                    fontSize: '15px',\r\n                    color: 'white',\r\n                    borderRight: '20px solid white'\r\n                  }}\r\n                >\r\n                  <div>% of<br />Income</div>\r\n                </th>\r\n\r\n                {/* Variance Group */}\r\n                <th\r\n                  className=\"text-center text-white p-2 font-bold text-sm\"\r\n                  style={{\r\n                    backgroundColor: headerBgColor,\r\n                    ...contentTextStyle,\r\n                    fontSize: '15px',\r\n                    color: 'white'\r\n                  }}\r\n                >\r\n                  $ Variance\r\n                </th>\r\n                <th\r\n                  className=\"text-center text-white p-2 font-bold text-sm\"\r\n                  style={{\r\n                    backgroundColor: headerBgColor,\r\n                    ...contentTextStyle,\r\n                    fontSize: '15px',\r\n                    color: 'white'\r\n                  }}\r\n                >\r\n                  % Variance\r\n                </th>\r\n              </tr>\r\n            </thead>\r\n            <tbody className=\"table-row-group\">\r\n              {tableData.map((item, index) => renderTableRow(item, index))}\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n        <div className='text-center text-slate-300 text-xs border-b-4 border-blue-900 py-9'>\r\n          <p>\r\n            The information contained in this report is provided for informational purposes only and is not intended to substitute for obtaining accounting, tax, or financial advice from a professional accountant. Any tax advice\r\n            contained in this report is not intended to be used for the purpose of avoiding penalties under tax law. While we use reasonable efforts to furnish accurate and up-to-date information, we do not warrant that any\r\n            information contained in or made available through this report is accurate, complete, reliable, current or error-free. We assume no liability or responsibility for any errors or omissions in the content of this report or\r\n            delivered information.\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProfitLossMonthlyDashboard;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,0BAA0B,GAAGA,CAAC;EAClCC,eAAe,GAAG,CAAC,CAAC;EACpBC,mBAAmB,GAAG,CAAC,CAAC;EACxBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,UAAU,GAAG,IAAI,CAAC;AACpB,CAAC,KAAK;EACJ;EACA,MAAMC,aAAa,GAAGJ,eAAe,CAACK,KAAK,IAAI,SAAS;;EAExD;EACA,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;IACpC,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACC,oBAAoB,EAAE;MAC7C,OAAO;QAAEC,SAAS,EAAE,EAAE;QAAEC,SAAS,EAAE;UAAEC,OAAO,EAAE,QAAQ;UAAEC,QAAQ,EAAE;QAAS;MAAE,CAAC;IAChF;IAEA,MAAMC,OAAO,GAAGN,OAAO,CAACC,oBAAoB;;IAE5C;IACA,IAAIM,YAAY,GAAG,QAAQ;IAC3B,IAAIC,aAAa,GAAG,QAAQ;IAE5B,IAAIF,OAAO,CAACG,MAAM,GAAG,CAAC,EAAE;MACtB,MAAMC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACJ,OAAO,CAAC,CAAC,CAAC,CAAC;MACpC,MAAMM,UAAU,GAAGF,IAAI,CAACG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,QAAQ,CAAC,UAAU,CAAC,CAAC;MAC7D,IAAIH,UAAU,EAAE;QACd,MAAMI,UAAU,GAAGJ,UAAU,CAACK,KAAK,CAAC,yBAAyB,CAAC;QAC9D,IAAID,UAAU,EAAE;UACdT,YAAY,GAAGS,UAAU,CAAC,CAAC,CAAC,CAACE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;UAC9C;UACA,MAAM,CAACC,KAAK,EAAEC,IAAI,CAAC,GAAGb,YAAY,CAACc,KAAK,CAAC,GAAG,CAAC;UAC7Cb,aAAa,GAAG,GAAGW,KAAK,IAAIG,QAAQ,CAACF,IAAI,CAAC,GAAG,CAAC,EAAE;QAClD;MACF;IACF;;IAEA;IACA,MAAMG,WAAW,GAAGjB,OAAO,CAACkB,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;MAChD,MAAMC,WAAW,GAAGD,IAAI,CAACE,YAAY;MACrC,IAAI,CAACH,GAAG,CAACE,WAAW,CAAC,EAAE;QACrBF,GAAG,CAACE,WAAW,CAAC,GAAG,EAAE;MACvB;MACAF,GAAG,CAACE,WAAW,CAAC,CAACE,IAAI,CAACH,IAAI,CAAC;MAC3B,OAAOD,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEN;IACA,MAAMvB,SAAS,GAAG,EAAE;IAEpBS,MAAM,CAACD,IAAI,CAACa,WAAW,CAAC,CAACO,OAAO,CAACH,WAAW,IAAI;MAC9C;MACAzB,SAAS,CAAC2B,IAAI,CAAC;QACbE,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAEL;MACZ,CAAC,CAAC;;MAEF;MACAJ,WAAW,CAACI,WAAW,CAAC,CAACG,OAAO,CAACJ,IAAI,IAAI;QACvC;QACA,MAAMO,OAAO,GAAGP,IAAI,CAACQ,YAAY,CAACC,WAAW,CAAC,CAAC,CAACpB,QAAQ,CAAC,OAAO,CAAC;;QAEjE;QACA,MAAMqB,cAAc,GAAGC,UAAU,CAACX,IAAI,CAACY,eAAe,IAAI,GAAG,CAAC;QAC9D,MAAMC,eAAe,GAAGF,UAAU,CAACX,IAAI,CAACc,mBAAmB,IAAI,GAAG,CAAC;QACnE,MAAMC,UAAU,GAAGL,cAAc,GAAG,CAAC;QACrC,MAAMM,UAAU,GAAGN,cAAc,GAAG,CAAC;QAErClC,SAAS,CAAC2B,IAAI,CAAC;UACbc,KAAK,EAAEjB,IAAI,CAACQ,YAAY;UACxBU,KAAK,EAAElB,IAAI,CAACmB,cAAc,IAAI,GAAG;UAAE;UACnCC,YAAY,EAAEpB,IAAI,CAACqB,wBAAwB,IAAI,IAAI;UACnDC,KAAK,EAAEtB,IAAI,CAACuB,iBAAiB,IAAI,GAAG;UACpCC,YAAY,EAAExB,IAAI,CAACyB,wBAAwB,IAAI,IAAI;UACnDC,QAAQ,EAAE1B,IAAI,CAACY,eAAe,IAAI,GAAG;UACrCC,eAAe,EAAEb,IAAI,CAACc,mBAAmB,IAAI,IAAI;UACjDP,OAAO,EAAEA,OAAO;UAChBoB,QAAQ,EAAE,CAACpB,OAAO,IAAI,CAACP,IAAI,CAACQ,YAAY,CAACC,WAAW,CAAC,CAAC,CAACpB,QAAQ,CAAC,OAAO,CAAC;UACxE0B,UAAU,EAAEA,UAAU;UACtBC,UAAU,EAAEA;QACd,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO;MACLxC,SAAS;MACTC,SAAS,EAAE;QACTC,OAAO,EAAEG,YAAY;QACrBF,QAAQ,EAAEG;MACZ;IACF,CAAC;EACH,CAAC;;EAED;EACA,MAAM;IAAEN,SAAS;IAAEC;EAAU,CAAC,GAAGJ,gBAAgB,CAACH,UAAU,CAAC;;EAE7D;EACA,IAAI,CAACA,UAAU,IAAI,CAACA,UAAU,CAACK,oBAAoB,IAAIC,SAAS,CAACO,MAAM,KAAK,CAAC,EAAE;IAC7E,oBACElB,OAAA;MAAK+D,SAAS,EAAC,8CAA8C;MAAAC,QAAA,eAC3DhE,OAAA;QAAK+D,SAAS,EAAC,yDAAyD;QAAAC,QAAA,gBACtEhE,OAAA;UAAAgE,QAAA,GAAK,KAAG,eACNhE,OAAA;YAAIiE,KAAK,EAAE;cAAE,GAAG9D,mBAAmB;cAAE+D,UAAU,EAAE,SAAS;cAAE3D,KAAK,EAAE;YAAQ,CAAE;YAAAyD,QAAA,EAAC;UAE9E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLtE,OAAA;YAAKiE,KAAK,EAAE;cAAE,GAAG9D,mBAAmB;cAAE+D,UAAU,EAAE,SAAS;cAAE3D,KAAK,EAAE;YAAQ,CAAE;YAAAyD,QAAA,EAAC;UAE/E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtE,OAAA;YAAKiE,KAAK,EAAE;cAAE,GAAG7D,gBAAgB;cAAEmE,QAAQ,EAAE;YAAO,CAAE;YAAAP,QAAA,EAAC;UAEvD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtE,OAAA;UAAK+D,SAAS,EAAC,uCAAuC;UAAAC,QAAA,eACpDhE,OAAA;YAAK+D,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BhE,OAAA;cAAK+D,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9DtE,OAAA;cAAK+D,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAA6B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5E;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAME,cAAc,GAAGA,CAACrC,IAAI,EAAEsC,KAAK,KAAK;IACtC,IAAItC,IAAI,CAACK,QAAQ,EAAE;MACjB,oBACExC,OAAA;QAAgB+D,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACjDhE,OAAA;UAAI+D,SAAS,EAAC,4BAA4B;UAACE,KAAK,EAAE;YAAE,GAAG7D,gBAAgB;YAAEmE,QAAQ,EAAE;UAAO,CAAE;UAAAP,QAAA,eAC1FhE,OAAA;YAAAgE,QAAA,EAAS7B,IAAI,CAACM;UAAQ;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACLtE,OAAA;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtE,OAAA;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtE,OAAA;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtE,OAAA;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtE,OAAA;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtE,OAAA;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA,GATFG,KAAK;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUV,CAAC;IAET;IAEA,IAAInC,IAAI,CAACO,OAAO,EAAE;MAChB,oBACE1C,OAAA;QAAgB+D,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBAChDhE,OAAA;UAAI+D,SAAS,EAAC,4BAA4B;UAACE,KAAK,EAAE;YAAE,GAAG7D,gBAAgB;YAAEmE,QAAQ,EAAE;UAAO,CAAE;UAAAP,QAAA,eAC1FhE,OAAA;YAAAgE,QAAA,EAAS7B,IAAI,CAACiB;UAAK;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACLtE,OAAA;UAAI+D,SAAS,EAAC,sBAAsB;UAACE,KAAK,EAAE;YAAE,GAAG7D,gBAAgB;YAAEmE,QAAQ,EAAE;UAAO,CAAE;UAAAP,QAAA,eACpFhE,OAAA;YAAAgE,QAAA,EAAS7B,IAAI,CAACkB;UAAK;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACLtE,OAAA;UAAI+D,SAAS,EAAC,YAAY;UAACE,KAAK,EAAE;YAAE,GAAG7D,gBAAgB;YAAEmE,QAAQ,EAAE;UAAO,CAAE;UAAAP,QAAA,eAC1EhE,OAAA;YAAAgE,QAAA,EAAS7B,IAAI,CAACoB;UAAY;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eACLtE,OAAA;UAAI+D,SAAS,EAAC,sBAAsB;UAACE,KAAK,EAAE;YAAE,GAAG7D,gBAAgB;YAAEmE,QAAQ,EAAE;UAAO,CAAE;UAAAP,QAAA,eACpFhE,OAAA;YAAAgE,QAAA,EAAS7B,IAAI,CAACsB;UAAK;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACLtE,OAAA;UAAI+D,SAAS,EAAC,YAAY;UAACE,KAAK,EAAE;YAAE,GAAG7D,gBAAgB;YAAEmE,QAAQ,EAAE;UAAO,CAAE;UAAAP,QAAA,eAC1EhE,OAAA;YAAAgE,QAAA,EAAS7B,IAAI,CAACwB;UAAY;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eACLtE,OAAA;UACE+D,SAAS,EAAE,wBAAwB5B,IAAI,CAACe,UAAU,GAAG,gBAAgB,GAAGf,IAAI,CAACgB,UAAU,GAAG,cAAc,GAAG,EAAE,EAAG;UAChHc,KAAK,EAAE;YAAE,GAAG7D,gBAAgB;YAAEmE,QAAQ,EAAE;UAAO,CAAE;UAAAP,QAAA,eAEjDhE,OAAA;YAAAgE,QAAA,EAAS7B,IAAI,CAAC0B;UAAQ;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACLtE,OAAA;UACE+D,SAAS,EAAE,cAAc5B,IAAI,CAACe,UAAU,GAAG,gBAAgB,GAAGf,IAAI,CAACgB,UAAU,GAAG,cAAc,GAAG,EAAE,EAAG;UACtGc,KAAK,EAAE;YAAE,GAAG7D,gBAAgB;YAAEmE,QAAQ,EAAE;UAAO,CAAE;UAAAP,QAAA,eAEjDhE,OAAA;YAAAgE,QAAA,EAAS7B,IAAI,CAACa;UAAe;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC;MAAA,GA3BEG,KAAK;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA4BV,CAAC;IAET;IAEA,oBACEtE,OAAA;MAAAgE,QAAA,gBACEhE,OAAA;QACE+D,SAAS,EAAE,yBAAyB5B,IAAI,CAAC2B,QAAQ,GAAG,MAAM,GAAG,MAAM,EAAG;QACtEG,KAAK,EAAE;UAAE,GAAG7D,gBAAgB;UAAEmE,QAAQ,EAAE;QAAO,CAAE;QAAAP,QAAA,EAEhD7B,IAAI,CAACiB;MAAK;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACLtE,OAAA;QAAI+D,SAAS,EAAC,sBAAsB;QAACE,KAAK,EAAE;UAAE,GAAG7D,gBAAgB;UAAEmE,QAAQ,EAAE;QAAO,CAAE;QAAAP,QAAA,EACnF7B,IAAI,CAACkB;MAAK;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACLtE,OAAA;QAAI+D,SAAS,EAAC,YAAY;QAACE,KAAK,EAAE;UAAE,GAAG7D,gBAAgB;UAAEmE,QAAQ,EAAE;QAAO,CAAE;QAAAP,QAAA,EACzE7B,IAAI,CAACoB;MAAY;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eACLtE,OAAA;QAAI+D,SAAS,EAAC,sBAAsB;QAACE,KAAK,EAAE;UAAE,GAAG7D,gBAAgB;UAAEmE,QAAQ,EAAE;QAAO,CAAE;QAAAP,QAAA,EACnF7B,IAAI,CAACsB;MAAK;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACLtE,OAAA;QAAI+D,SAAS,EAAC,YAAY;QAACE,KAAK,EAAE;UAAE,GAAG7D,gBAAgB;UAAEmE,QAAQ,EAAE;QAAO,CAAE;QAAAP,QAAA,EACzE7B,IAAI,CAACwB;MAAY;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eACLtE,OAAA;QACE+D,SAAS,EAAE,wBAAwB5B,IAAI,CAACe,UAAU,GAAG,gBAAgB,GAAGf,IAAI,CAACgB,UAAU,GAAG,cAAc,GAAG,EAAE,EAAG;QAChHc,KAAK,EAAE;UAAE,GAAG7D,gBAAgB;UAAEmE,QAAQ,EAAE;QAAO,CAAE;QAAAP,QAAA,EAEhD7B,IAAI,CAAC0B;MAAQ;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eACLtE,OAAA;QACE+D,SAAS,EAAE,cAAc5B,IAAI,CAACe,UAAU,GAAG,gBAAgB,GAAGf,IAAI,CAACgB,UAAU,GAAG,cAAc,GAAG,EAAE,EAAG;QACtGc,KAAK,EAAE;UAAE,GAAG7D,gBAAgB;UAAEmE,QAAQ,EAAE;QAAO,CAAE;QAAAP,QAAA,EAEhD7B,IAAI,CAACa;MAAe;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA,GA9BEG,KAAK;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OA+BV,CAAC;EAET,CAAC;EAED,oBACEtE,OAAA;IAAK+D,SAAS,EAAC,8CAA8C;IAAAC,QAAA,eAE3DhE,OAAA;MAAK+D,SAAS,EAAC,0EAA0E;MAAAC,QAAA,gBAEvFhE,OAAA;QAAAgE,QAAA,gBACEhE,OAAA;UACEiE,KAAK,EAAE;YAAE,GAAG9D,mBAAmB;YAAE+D,UAAU,EAAE,SAAS;YAAE3D,KAAK,EAAE;UAAQ,CAAE;UAAAyD,QAAA,EAC1E;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLtE,OAAA;UACEiE,KAAK,EAAE;YAAE,GAAG9D,mBAAmB;YAAE+D,UAAU,EAAE,SAAS;YAAE3D,KAAK,EAAE;UAAQ,CAAE;UAAAyD,QAAA,EAExEpD,SAAS,CAACC;QAAO;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACNtE,OAAA;UACEiE,KAAK,EAAE;YAAE,GAAG7D,gBAAgB;YAAEmE,QAAQ,EAAE;UAAO,CAAE;UAAAP,QAAA,EAClD;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtE,OAAA;QAAK+D,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BhE,OAAA;UAAO+D,SAAS,EAAC,uDAAuD;UAAAC,QAAA,gBACtEhE,OAAA;YAAO+D,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBAEnChE,OAAA;cAAI+D,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC7ChE,OAAA;gBACE+D,SAAS,EAAC,6BAA6B;gBACvCE,KAAK,EAAE;kBAAE,GAAG7D,gBAAgB;kBAAEmE,QAAQ,EAAE,MAAM;kBAAEhE,KAAK,EAAE;gBAAQ;cAAE;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACNtE,OAAA;gBACE+D,SAAS,EAAC,mCAAmC;gBAC7CE,KAAK,EAAE;kBAAE,GAAG7D,gBAAgB;kBAAEmE,QAAQ,EAAE,MAAM;kBAAEhE,KAAK,EAAE;gBAAQ,CAAE;gBACjEmE,OAAO,EAAC,GAAG;gBAAAV,QAAA,EAEVpD,SAAS,CAACC;cAAO;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACLtE,OAAA;gBACE+D,SAAS,EAAC,mCAAmC;gBAC7CE,KAAK,EAAE;kBAAE,GAAG7D,gBAAgB;kBAAEmE,QAAQ,EAAE,MAAM;kBAAEhE,KAAK,EAAE;gBAAQ,CAAE;gBACjEmE,OAAO,EAAC,GAAG;gBAAAV,QAAA,EAEVpD,SAAS,CAACE;cAAQ;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eACLtE,OAAA;gBACE+D,SAAS,EAAC,6BAA6B;gBACvCE,KAAK,EAAE;kBAAE,GAAG7D,gBAAgB;kBAAEmE,QAAQ,EAAE,MAAM;kBAAEhE,KAAK,EAAE;gBAAQ,CAAE;gBACjEmE,OAAO,EAAC;cAAG;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAELtE,OAAA;cAAAgE,QAAA,gBACEhE,OAAA;gBACE+D,SAAS,EAAC,6CAA6C;gBACvDE,KAAK,EAAE;kBACL,GAAG7D,gBAAgB;kBACnBmE,QAAQ,EAAE,MAAM;kBAChBhE,KAAK,EAAE;gBACT;cAAE;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENtE,OAAA;gBACE+D,SAAS,EAAC,8CAA8C;gBACxDE,KAAK,EAAE;kBACLU,eAAe,EAAErE,aAAa;kBAC9B,GAAGF,gBAAgB;kBACnBmE,QAAQ,EAAE,MAAM;kBAChBhE,KAAK,EAAE;gBACT,CAAE;gBAAAyD,QAAA,EACH;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLtE,OAAA;gBACE+D,SAAS,EAAC,8CAA8C;gBACxDE,KAAK,EAAE;kBACLU,eAAe,EAAErE,aAAa;kBAC9B,GAAGF,gBAAgB;kBACnBmE,QAAQ,EAAE,MAAM;kBAChBhE,KAAK,EAAE,OAAO;kBACdqE,WAAW,EAAE;gBACf,CAAE;gBAAAZ,QAAA,eAEFhE,OAAA;kBAAAgE,QAAA,GAAK,MAAI,eAAAhE,OAAA;oBAAAmE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,UAAM;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eAGLtE,OAAA;gBACE+D,SAAS,EAAC,8CAA8C;gBACxDE,KAAK,EAAE;kBACLU,eAAe,EAAErE,aAAa;kBAC9B,GAAGF,gBAAgB;kBACnBmE,QAAQ,EAAE,MAAM;kBAChBhE,KAAK,EAAE;gBACT,CAAE;gBAAAyD,QAAA,EACH;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLtE,OAAA;gBACE+D,SAAS,EAAC,8CAA8C;gBACxDE,KAAK,EAAE;kBACLU,eAAe,EAAErE,aAAa;kBAC9B,GAAGF,gBAAgB;kBACnBmE,QAAQ,EAAE,MAAM;kBAChBhE,KAAK,EAAE,OAAO;kBACdqE,WAAW,EAAE;gBACf,CAAE;gBAAAZ,QAAA,eAEFhE,OAAA;kBAAAgE,QAAA,GAAK,MAAI,eAAAhE,OAAA;oBAAAmE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,UAAM;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eAGLtE,OAAA;gBACE+D,SAAS,EAAC,8CAA8C;gBACxDE,KAAK,EAAE;kBACLU,eAAe,EAAErE,aAAa;kBAC9B,GAAGF,gBAAgB;kBACnBmE,QAAQ,EAAE,MAAM;kBAChBhE,KAAK,EAAE;gBACT,CAAE;gBAAAyD,QAAA,EACH;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLtE,OAAA;gBACE+D,SAAS,EAAC,8CAA8C;gBACxDE,KAAK,EAAE;kBACLU,eAAe,EAAErE,aAAa;kBAC9B,GAAGF,gBAAgB;kBACnBmE,QAAQ,EAAE,MAAM;kBAChBhE,KAAK,EAAE;gBACT,CAAE;gBAAAyD,QAAA,EACH;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRtE,OAAA;YAAO+D,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC/BrD,SAAS,CAACkE,GAAG,CAAC,CAAC1C,IAAI,EAAEsC,KAAK,KAAKD,cAAc,CAACrC,IAAI,EAAEsC,KAAK,CAAC;UAAC;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNtE,OAAA;QAAK+D,SAAS,EAAC,oEAAoE;QAAAC,QAAA,eACjFhE,OAAA;UAAAgE,QAAA,EAAG;QAKH;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACQ,EAAA,GA3WI7E,0BAA0B;AA6WhC,eAAeA,0BAA0B;AAAC,IAAA6E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}